<template>
    <div id="materialDetail">
        <main>
            <section class="top-bg">
                <u-swiper v-if="detail.bannerImagUrl && detail.bannerImagUrl.length" :list="detail.bannerImagUrl" img-mode="aspectFit" height="400" bg-color="#000"></u-swiper>
                <image v-else src="../static/images/giftBagBgc.jpg" mode="widthFix" />
                <p>
                    <span>{{ detail.activityName }}</span> <span>¥{{ detail.unitPrice | toDecimal2 }}</span>
                </p>
            </section>

            <div class="line"></div>
            <text class="title">礼包详情</text>
            <div class="detail-imgs" v-if="detail.detailImagUrl && detail.detailImagUrl.length">
                <u-image v-for="(imgUrl, key) in detail.detailImagUrl" :key="key" width="100%" mode="widthFix" :src="imgUrl" />
            </div>
            <section v-else class="content">
                <p>
                    礼包内容: 设备兑换券 <span>{{ detail.onceMinLimit }}</span> 张
                </p>
                <p>
                    兑换券单价: <span>{{ detail.unitPrice | toDecimal2 }}</span> 元/张
                </p>
                <p>
                    激活奖励: 持有人: <span>{{ detail.activateReturnHolderAmount | toDecimal2 }}</span> 元/张 采购人: <span>{{ detail.activateReturnBuyerAmount | toDecimal2 }}</span> 元/张
                </p>
                <p>
                    激活标准: 商户手刷 <span>{{ detail.activateMinAmount | toDecimal2 }}</span> 元缴纳服务费后即为激活
                </p>
                <p>
                    达标奖励: 持有人: <span>{{ detail.standardReturnHolderAmount | toDecimal2 }}</span> 元/张 采购人: <span>{{ detail.standardReturnBuyerAmount | toDecimal2 }}</span> 元/张
                </p>
                <p>
                    达标标准: 商户达标累计交易额至 <span>{{ detail.standardTotalAmount | toDecimal2 }}</span> 元后即为达标
                </p>
                <p>
                    活动有效期: 自采购之日起 <span>{{ detail.standardValidDays }}</span> 天
                </p>
            </section>
        </main>
        <footer class="custom-button">
            <button @click="confirmOrder">立即购买</button>
        </footer>
    </div>
</template>

<script>
import { toDecimal2 } from '../../static/utils/date'

export default {
    name: 'MaterialDetail',
    filters: {
        toDecimal2
    },
    data() {
        return {
            detail: {}
        }
    },

    onLoad() {
        this.detail = JSON.parse(this.$Route.query.detail) || {}
    },
    methods: {
        confirmOrder() {
            this.$Router.push({ name: 'ConfirmOrder', params: { detail: this.$Route.query.detail } })
        }
    }
}
</script>

<style lang="less" scoped>
#materialDetail {
    > main {
        background-color: #fff;
        .top-bg {
            > image {
                width: 100%;
            }
            > p {
                display: flex;
                justify-content: space-between;
                padding: 20rpx 30rpx;
                margin: 0;
                font-weight: 450;
                font-size: 30rpx;
                > span {
                    &:last-of-type {
                        margin-left: 0.2em;
                        font-size: 36rpx;
                        color: rgb(226, 63, 3);
                    }
                }
            }
        }
        .content {
            padding: 30rpx;

            > p {
                margin: 12rpx 0;
                color: #333;
                span {
                    font-weight: bold;
                }
            }
        }

        .title {
            display: block;
            padding: 30rpx;
            font-size: 28rpx;
            font-weight: 500;
            color: #999;
        }
        .detail-imgs {
            font-size: 0;
        }
    }
    .line {
        width: 100%;
        height: 20rpx;
        background-color: #f3f5f7;
    }
    > footer {
        position: fixed;
        bottom: 0;
        left: 0;
        background-color: #fff;
        border-top: 1rpx solid #f3f5f7;
    }
    .custom-button {
        button {
            width: 540rpx;
            background-color: rgb(221, 6, 6);
        }
    }
}
</style>
