<template>
  <div id="materialOrderDetail">
    <main>
      <section>
        <div>
          <p>
            <span>{{ detail.orderNo }}</span
            ><span>{{
              detail.orderStatus == '0' ? '待发货' : detail.orderStatus == '1' ? '已发货' : detail.orderStatus == '2' ? '已收货' : ''
            }}</span>
          </p>
          <div>
            <p>
              <span>礼包名称</span> <span>{{ detail.activityName }}</span>
            </p>
            <p>
              <span>物料数量</span> <span>{{ detail.orderQuantity }}</span>
            </p>
            <p>
              <span>交易金额</span> <span><span style="margin-right: 0.2em">¥</span>{{ detail.orderAmount | toDecimal2 }}</span>
            </p>
            <p>
              <span>代理商名称</span> <span>{{ detail.agentName }}</span>
            </p>
          </div>
          <p>{{ detail.createTime ? dateFormat(detail.createTime) : '' }}</p>
        </div>
        <div>
          <p>
            <span>收件人</span> <span>{{ detail.recipientPeople }}</span>
          </p>
          <p>
            <span>联系电话</span> <span>{{ detail.mobilePhone }}</span>
          </p>
          <p>
            <span>收件地址</span> <span>{{ detail.address + detail.fullAddress }}</span>
          </p>
        </div>
        <div v-if="detail.orderStatus == '1' || detail.orderStatus == '2'">
          <p>
            <span>快递类型</span> <span>{{ detail.expressTypeStr }}</span>
          </p>
          <p>
            <span>物流公司</span> <span>{{ detail.logisticsCompany }}</span>
          </p>
          <p>
            <span>物流单号</span> <span>{{ detail.logisticsNum }}</span>
          </p>
        </div>
        <div>
          <p>
            <span>付款方式</span> <span>{{ payMethodMap[detail.payMethod] }}</span>
          </p>
          <template v-if="detail.payMethod == 0">
            <p>转账凭证图片:</p>
            <image :src="detail.transferVoucherImgPath" mode="widthFix" />
          </template>
        </div>
      </section>
    </main>
  </div>
</template>

<script>
import { queryDetailOrder } from '../../http/api-direct';
import { dateFormat, toDecimal2 } from '../../static/utils/date';

export default {
  name: 'MaterialOrderDetail',
  filters: {
    dateFormat,
    toDecimal2
  },
  data() {
    return {
      detail: {
        orderAmount: '0'
      },
      dateFormat,
      payMethodMap: {
        4: '余额支付',
        0: '线下付款',
        1: '兑换券支付'
      }
    };
  },
  onLoad() {
    this.getDetail();
  },
  methods: {
    getDetail() {
      queryDetailOrder(this.$Route.query.id).then(res => {
        if (res.code == '00') {
          this.detail = res.data;
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
#materialOrderDetail {
  > main {
    > section {
      display: flex;
      flex-direction: column;
      overflow-y: scroll;
      padding: 20rpx;
      > div {
        background-color: #fff;
        border-radius: 8rpx;
        margin-bottom: 20rpx;
        padding: 20rpx;
        p {
          display: flex;
          margin: 6rpx 0;
          > span {
            &:first-of-type {
              width: 6em;
              flex-shrink: 0;
              color: #666;
            }
            &:last-of-type {
              color: #333;
            }
          }
        }
        &:first-of-type {
          padding: 0;
          > p {
            display: flex;
            justify-content: space-between;
            padding: 20rpx;
            margin: 0;
            border-bottom: 2rpx solid #f3f5f7;
            > span {
              &:first-of-type {
                width: auto;
                flex-shrink: 0;
              }
            }
            &:first-of-type {
              > span {
                font-size: 24rpx;
                color: #666;
              }
              .orderStatus0 {
                color: #666;
              }
              .orderStatus1 {
                color: green;
              }
              .orderStatus2 {
                color: rgb(219, 61, 4);
              }
            }
            &:last-of-type {
              justify-content: flex-end;
              border: none;
              border-top: 2rpx solid #f3f5f7;
              font-size: 24rpx;
              color: #999;
            }
          }
          > div {
            padding: 20rpx;
          }
        }
        &.timeAndSatus {
          > p {
            justify-content: space-between;
            > span {
              &:first-of-type {
                width: auto;
              }
            }
          }
        }
        &:last-of-type {
          flex: 1;
          margin-bottom: 0;
          text-align: center;
          > p {
            margin-bottom: 30rpx;
            color: #333;
            &:first-of-type {
              text-align: left;
            }
          }
          > image {
            width: 80%;
          }
        }
      }
    }
  }
}
</style>
