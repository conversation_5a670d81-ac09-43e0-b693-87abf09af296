<template>
    <div id="address">
        <main>
            <no-content v-if="show" />

            <div v-show="!show" v-for="(a, index) in addressList" :key="index" class="address-item" @click="selectAddress(a)">
                <section>
                    <div>
                        <div><span>{{ a.recipientName}}</span><span>{{a.mobilePhone}}</span> </div>
                        <div><span v-if="a.isDefaultAddressFlag == 1">默认</span>{{a.address + a.fullAddress }}</div>
                    </div>
                    <p @click.stop="editAddress(a)">
                        <u-icon name="edit-pen" size='40' />
                    </p>
                </section>
                <section>
                    <span v-show="a.isDefaultAddressFlag == 0" @click.stop="setDefault(a)">设为默认</span>
                    <span @click.stop="deleteAddress(a.id)">删除</span>
                </section>
            </div>
        </main>
        <footer class="custom-button">
            <button @click="addAddress">添加收货地址</button>
        </footer>
    </div>
</template>

<script>
import { getExpressInfoList, editExpress, deleteExpress } from '../../../http/api-direct'

export default {
    name: "Address",
    data() {
        return {
            show: false,
            isSelect: false,
            addressList: []
        }
    },
    onLoad() {
        this.isSelect = this.$Route.query.type == '0'
    },
    onShow() {
        this.getAddressList()
    },
    onReady() {
        uni.setNavigationBarTitle({
            title: this.isSelect ? '选择收货地址' : '我的收货地址'
        });
    },
    methods: {
        selectAddress(item) {
            if (this.isSelect) {
                uni.$emit('select-address', item);
                this.$Router.back(1);
            }
        },
        editAddress(item) {
            this.$Router.push({ name: 'EditAddress', params: { addressInfo: JSON.stringify(item) } });
        },
        addAddress() {
            this.$Router.push({ name: 'EditAddress', params: null });
        },
        deleteAddress(id) {
            uni.showModal({
                content: '确定要删除此地址吗?',
                success: (res) => {
                    if (res.confirm) {
                        deleteExpress(id).then(res => {
                            if (res.code == '00') {
                                this.getAddressList();
                            }
                        })
                    }
                }
            });
        },
        setDefault(item) {
            var params = Object.assign({}, item);
            params.isDefaultAddressFlag = 1;
            editExpress(params).then(res => {
                if (res.code == '00') {
                    this.getAddressList();
                }
            })

        },
        getAddressList() {
            getExpressInfoList().then(res => {
                if (res.code == '00') {
                    if (res.data.length != 0) {
                        this.addressList = res.data;
                    } else {
                        this.show = true;
                    }
                }
            })

        }
    }
}
</script>

<style lang="less" scoped>
#address {
    padding: 20rpx 0 100rpx;
    > main {
        .address-item {
            padding: 0 30rpx;
            margin: 0 20rpx 20rpx;
            border-radius: 10rpx;
            background-color: #fff;
            > section {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 26rpx 0;
                &:first-of-type {
                    border-bottom: 1px solid #e5e5e5;
                    > div {
                        > div {
                            &:first-of-type {
                                > span {
                                    &:last-of-type {
                                        color: #666;
                                        margin-left: 0.5em;
                                    }
                                }
                            }
                            &:last-of-type {
                                display: flex;
                                margin-top: 10rpx;
                                color: #666;
                                font-size: 26rpx;
                                align-items: center;
                                > span {
                                    flex-shrink: 0;
                                    padding: 0 8rpx;
                                    border-radius: 4rpx;
                                    font-size: 24rpx;
                                    color: #333;
                                    color: #004ea9;
                                    border: 2rpx solid #004ea9;
                                    margin-right: 10rpx;
                                }
                            }
                        }
                    }
                }
                &:last-of-type {
                    position: relative;
                    height: 88rpx;
                    > span {
                        font-size: 24rpx;
                        position: absolute;
                        line-height: 36rpx;
                        top: 20rpx;
                        &:first-of-type {
                            left: 0;
                            padding: 6rpx 20rpx;
                            border-radius: 6rpx;
                            color: #fff;
                            background-color: #004ea9;
                        }
                        &:last-of-type {
                            padding: 6rpx 0;
                            right: 0;
                            color: #666;
                        }
                    }
                }
            }
        }
    }
    > footer {
        position: fixed;
        bottom: 0;
        left: 0;
        background-color: #fff;
    }
    .custom-button {
        button {
            width: 500rpx;
        }
    }
}
</style>