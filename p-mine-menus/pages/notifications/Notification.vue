<template>
    <div id="notification">
        <main>
            <p>{{ notification.noticeTitle }}</p>
            <p class="content"> <rich-text :nodes="notification.noticeContent"></rich-text> </p>
            <p>{{ notification.createTime }}</p>
        </main>
    </div>
</template>

<script>
import { getNoticeById } from '../../../http/api'
import { dateFormat } from '../../../static/utils/date'

export default {
    name: 'Notification',
    data() {
        return {
            notification: {
                noticeTitle: '',
                noticeContent: '',
                createTime: ''
            }
        }
    },
    onLoad() {
        getNoticeById(this.$Route.query.id).then(res => {
            if (res.code == '00') {
                this.notification = {
                    noticeTitle: res.data.noticeTitle,
                    noticeContent: res.data.noticeContent.replace(/\<img/gi, '<image style="max-width:100%;height:auto"'),
                    createTime: dateFormat(res.data.createTime,'YYYY-MM-DD')
                }
                const appNotifications = this.$store.state.appNotifications;
                appNotifications[appNotifications.findIndex(i => i.id == this.$Route.query.id && i.type == this.$Route.query.announceType)].readingSatus = 1;
                this.$store.commit('SET_APPNOTIFICATIONS', appNotifications);
            }
        })
    }
}
</script>

<style lang="less" scoped>
#notification {
    height: 100%;
    padding-top: 20rpx;
    main {
        min-height: 100%;
        padding: 20rpx 30rpx;
        background-color: #fff;
        > p {
            word-break: break-all;
            &:nth-of-type(1) {
                color: #004ea9;
                font-size: 30rpx;
                font-weight: 500;
                margin: 0;
            }
            &:nth-of-type(3) {
                text-align: right;
                margin-top: 10rpx;
                color: #888;
            }
        }
    }
	
		
	rich-text{
		white-space: pre-line;
        
	}
}
</style>