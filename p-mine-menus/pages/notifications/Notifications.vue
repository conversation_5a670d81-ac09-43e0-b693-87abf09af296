<template>
    <div id="notifications">
        <u-sticky>
            <u-tabs :list="types" :current="current" bar-width="80" active-color="#004ea9" @change="changeType" :is-scroll="false"></u-tabs>
        </u-sticky>
        <main>
            <no-content v-show="show" tips="暂无公告" mode="news" />

            <section v-show="!show">
                <div v-for="(n,index) in list" :key="index" @click="getDetail(n.id)">
                    <section>
                        <u-icon name="bell-fill" color="white" />
                        <u-badge is-dot v-if="n.readingSatus == 0" :offset="[16,16]" size="mini" />
                    </section>
                    <section>
                        <p>
                            {{n.noticeTitle}}
                        </p>
                        <!-- <p>{{n.noticeContent}}</p> -->
                        <p>{{n.createTime ?  dateFormat(n.createTime,'YYYY-MM-DD') : '-'}}</p>
                    </section>
                </div>
            </section>
        </main>
    </div>
</template>

<script>
import { getAllNotifications } from '../../../http/api'
import { dateFormat } from '../../../static/utils/date'

export default {
    name: 'Notifications',
    data() {
        return {
            current: 0,
            type: 0,
        }
    },
    computed: {
        appNotifications() {
            return this.$store.state.appNotifications
        },
        list() {
            return this.$store.state.appNotifications.filter(n => n.type === this.type)
        },
        show() {
            return !this.list.length
        },
        types() { return [{ name: '官方公告', value: 0, count: this.$store.state.appNotifications.filter(i => i.readingSatus == 0 && i.type == 0).length },{ name: '系统通知', value: 1, count: this.$store.state.appNotifications.filter(i => i.readingSatus == 0 && i.type == 1).length }]}

    },
    onLoad() {
        getAllNotifications().then(res => {
            if (res.code == '00') {
                var { officialNOtice = [], systemNotice = [] } = res.data
                officialNOtice ? officialNOtice.forEach(o => o.type = 0) : officialNOtice = []
                systemNotice ? systemNotice.forEach(s => s.type = 1) : systemNotice = []
                var appNotificationsMap = [...officialNOtice, ...systemNotice]
                const appNotifications = [];
                appNotificationsMap.forEach(i => {
                    appNotifications.push({
                        id: i.id,
                        type: i.type,
                        noticeTitle: i.noticeTitle,
                        noticeContent: i.noticeContent,
                        createTime: i.createTime,
                        readingSatus: 0 // 未读
                    })
                });

                if (uni.getStorageSync('appNotifications')) {
                    appNotifications.forEach(i => {
                        uni.getStorageSync('appNotifications').forEach(j => {
                            if (i.id == j.id && i.type == j.type) {
                                i.readingSatus = j.readingSatus;
                            }
                        })
                    });
                }

                this.$store.commit('SET_APPNOTIFICATIONS', appNotifications);
            }
        })
    },
    methods: {
        changeType(index) {
            this.current = index
            this.type = this.types[index]['value']
        },
        getDetail(id) {
            this.$Router.push({ name: 'Notification', params: { id, announceType: this.type } });
        },
        dateFormat
    }
}
</script>

<style lang="less" scoped>
#notifications {
    main {
        padding: 20rpx 0 0;
        > section {
            > div {
                display: flex;
                align-items: flex-start;
                background-color: white;
                padding: 30rpx 30rpx 20rpx;
                margin-bottom: 20rpx;
                > section {
                    &:first-of-type {
                        flex-shrink: 0;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        position: relative;
                        width: 34px;
                        height: 34px;
                        margin-right: 10px;
                        border-radius: 50%;
                        background-color: #004ea9;
                    }
                    &:last-of-type {
                        flex: 1;
                        min-width: 0;
                        > p {
                            margin: 0;
                            word-break: break-all;
                            &:nth-of-type(1) {
                                font-size: 16px;
                            }
                            &:nth-of-type(2) {
                                color: #666;
                                white-space: nowrap;
                                overflow: hidden;
                                text-overflow: ellipsis;
                            }
                            &:nth-of-type(3) {
                                color: #999;
                                margin-top: 5px;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>