<template>
    <div id="changeBankCard">
        <main>
            <section>
                <p>
                    <span>姓名</span>
                    <span>{{ userInfo.accountName }}</span>
                </p>
                <p v-if="userInfo.payChannelCode !== '1006'">
                    <span>身份证号码</span>
                    <span>{{ userInfo.idCardNo.replace(/(\d{3})\d*(\d{4})/, '$1 **** **** **** $2') }}</span>
                </p>
            </section>

            <u-form :model="bank" ref="uForm" label-width="150">
                <template v-if="userInfo.payChannelCode !== '1006'">
                    <u-form-item label="银行卡号" prop="bankCard">
                        <u-input v-model="bank.bankCard" placeholder="请输入银行卡号" />
                    </u-form-item>
                    <u-form-item label="预留手机号" prop="phone">
                        <u-input placeholder="请输入银行预留手机号" v-model="bank.phone" type="tel" />
                    </u-form-item>
                </template>

                <template v-else>
                    <u-form-item label="账户类型">
                        <u-radio-group v-model="bank.receiveType">
                            <u-radio :name="0">手机号</u-radio>
                            <u-radio :name="1">邮箱</u-radio>
                        </u-radio-group>
                    </u-form-item>
                    <u-form-item v-if="bank.receiveType === 1" label="支付宝账号(邮箱)" prop="email" key="email">
                        <u-input placeholder="请输入支付宝账号" v-model="bank.email" />
                    </u-form-item>
                    <u-form-item v-else label="支付宝账号(手机号)" prop="phone" key="phone">
                        <u-input placeholder="请输入支付宝账号" type="tel" v-model="bank.phone" />
                    </u-form-item>
                </template>
            </u-form>

            <view style="margin: 100rpx 30rpx 30rpx">
                <u-button type="primary" @click="submit">提 交</u-button>
            </view>
        </main>
    </div>
</template>

<script>
import { updateCard } from '../../../http/api'

const rules = {
    bankCard: [
        {
            required: true,
            message: '请输入银行卡号',
            trigger: ['change', 'blur']
        }
    ],
    phone: [
        {
            required: true,
            pattern: /^1[3456789]\d{9}$/,
            message: '手机号码格式不正确'
        }
    ],
    email: [
        {
            required: true,
            pattern: /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
            message: '邮箱格式不正确'
        }
    ]
}

export default {
    name: 'ChangeBankCard',
    data() {
        const _this = this
        return {
            bank: {
                receiveType: _this.$store.state.userInfo.receiveType || 0,
                payChannelCode: '', //代付通道(登陆返回)
                bankCard: '', //银行卡号
                email: _this.$store.state.userInfo.email || '',
                phone: _this.$store.state.userInfo.mobile || '' //银行卡预留手机号(当代付通道为1004时候显示此字段)
            }
        }
    },
    computed: {
        userInfo() {
            return this.$store.state.userInfo
        }
    },
    onReady() {
        this.$refs.uForm.setRules(rules)
    },
    methods: {
        submit() {
            this.$refs.uForm.validate(valid => {
                if (valid) {
                    this.bank.payChannelCode = this.userInfo.payChannelCode
                    const params = Object.assign(this.bank, {})
                    if (this.bank.payChannelCode === '1006' && this.bank.receiveType === 1) {
                        params.phone = params.email
                    }
                    updateCard(params).then(res => {
                        if (res.code == '00') {
                            uni.showToast({
                                title: '结算信息修改成功！',
                                icon: 'none'
                            })
                            if (this.bank.payChannelCode !== '1006') {
                                this.$store.state.userInfo.mobile = this.bank.phone
                                this.$store.state.userInfo.bankCardNo = this.bank.bankCard
                            } else {
                                if (this.bank.receiveType === 1) {
                                    this.$store.state.userInfo.email = this.bank.email
                                } else {
                                    this.$store.state.userInfo.mobile = this.bank.phone
                                }
                                this.$store.state.userInfo.receiveType = this.bank.receiveType
                            }
                            setTimeout(() => {
                                this.$Router.back(1)
                            }, 1500)
                        }
                    })
                }
            })
        }
    }
}
</script>

<style lang="less" scoped>
#changeBankCard {
    height: 100%;
    padding-top: 20rpx;

    > main {
        height: 100%;
        background: white;
        padding: 0 30rpx;

        > section {
            > p {
                padding: 30rpx 0;
                margin: 0;

                > span {
                    display: inline-block;

                    &:nth-of-type(1) {
                        width: 180rpx;
                        color: #666666;
                    }

                    &:nth-of-type(2) {
                        color: #222222;
                    }
                }

                &:nth-of-type(2) {
                    border-top: 2rpx solid #e6e6e6;
                }
            }
        }

        > p {
            height: 20rpx;
            background: #f8f8f8;
            margin: 0;
        }
    }
}
</style>
