<template>
    <div id="bill">
        <main>
            <no-content v-if="show" mode="order"/>

            <div v-for="(b, index) in bill" :key="index">
                <image :src="b.postType.includes('入账') ? enter : b.postType.includes('提现') ? out : adjust" alt="" />
                <section>
                    <p>{{b.postType}}</p>
                    <p>{{b.createTime}}</p>
                </section>
                <p>
                    <span>{{b.flowAmount}}</span>元
                </p>
            </div>

            <u-loadmore v-if="!show" :status="status" @loadmore="loadmore" />
        </main>
    </div>
</template>

<script>
import enter from "../static/images/in.png";
import out from "../static/images/out.png";
import adjust from "../static/images/adjust.png";
import {
    bill
} from "../../http/api";

export default {
    name: "Bill",
    data() {
        return {
            show: false,
            enter: enter,
            out: out,
            adjust: adjust,
            bill: [],
            total: 0,
            status: 'loading'
        }
    },
    onLoad() {
        bill({
            pageNo: 1,
            pageSize: 20
        }).then((res) => {
            if (res.code == "00") {
                if (res.data.list.length != 0) {
                    this.bill = res.data.list;
                    this.total = res.data.total;
                    if (this.bill.length >= this.total) {
                        // 数据全部加载完成
                        this.status = 'nomore';
                    } else {
                        this.status = 'loadmore';
                    }
                } else {
                    this.show = true;
                }
            }
        })
    },
    onReachBottom() {
        this.loadmore()
    },
    methods: {
        loadmore() {
            if (this.status == 'nomore') return;
            this.status = 'loading'
            bill({
                pageNo: this.bill.length / 20 + 1,
                pageSize: 20
            }).then((res) => {
                if (res.code == "00") {
                    this.total = res.data.total;
                    res.data.list.forEach((i) => {
                        this.bill.push(i);
                    })
                }
                if (this.bill.length >= this.total) {
                    // 数据全部加载完成
                    this.status = 'nomore';
                } else {
                    this.status = 'loadmore';
                }
            })
        }
    }
}
</script>

<style lang="less" scoped>
#bill {
    padding-top: 20rpx;
    > main {
        > div {
            background-color: white;
            display: flex;
            align-items: center;
            position: relative;
            > image {
                flex-shrink: 0;
                width: 68rpx;
                height: 68rpx;
                margin: 0 20rpx 0 30rpx;
            }

            > section {
                width: 100%;
                height: 128rpx;
                border-bottom: 2rpx solid #e5e5e5;
                padding-top: 34rpx;

                > p {
                    &:nth-of-type(1) {
                        color: #222222;
                        font-weight: 600;
                        margin: 0 0 10rpx 0;
                    }

                    &:nth-of-type(2) {
                        font-size: 24rpx;
                        color: #999999;
                        white-space: pre;
                        margin: 0;
                    }
                }
            }

            > p {
                font-size: 18rpx;
                color: #d62a1b;
                margin: 0;
                position: absolute;
                right: 30rpx;

                > span {
                    font-size: 30rpx;
                    font-weight: 600;
                }
            }
        }
    }
}
</style>
