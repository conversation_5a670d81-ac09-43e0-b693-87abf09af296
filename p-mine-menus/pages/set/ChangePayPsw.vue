<template>
    <div id="changePayPsw">
        <main>
            <u-form :model="password" ref="uForm" :label-width="170">
                <u-form-item label="旧密码" prop="oldPayPwd">
                    <u-input type="password" :password-icon="false" v-model="password.oldPayPwd" placeholder="请输入旧支付密码" :clearable="false" />
                </u-form-item>
                <u-form-item label="新密码" prop="payPwd">
                    <u-input type="password" :password-icon="false" v-model="password.payPwd" placeholder="请输入新支付密码" :clearable="false" />
                </u-form-item>
                <u-form-item label="确认新密码" prop="confirmPsw">
                    <u-input type="password" :password-icon="false" v-model="password.confirmPsw" placeholder="请确认新支付密码" :clearable="false" />
                </u-form-item>
            </u-form>

            <view style="margin:150rpx 30rpx 30rpx">
                <u-button type="primary" @click="submit">提 交</u-button>
            </view>
        </main>
    </div>
</template>

<script>
import { changePayPwd } from "../../../http/api"

export default {
    name: "ChangePayPsw",
    data() {
        return {
            pattern: /^[0-9]{6}$/,
            password: {
                oldPayPwd: null,
                payPwd: null,
                confirmPsw: null
            },
            confirmPsw: null,
            rules: {
                oldPayPwd: [{ required: true, pattern: /^[0-9]{6}$/, message: '密码必须6位数字', trigger: ['change', 'blur'], }],
                payPwd: [{ required: true, pattern: /^[0-9]{6}$/, message: '密码必须6位数字', trigger: ['change', 'blur'], }],
                confirmPsw: [{
                    required: true, validator: (rule, value, callback) => {
                        return /^[0-9]{6}$/.test(value) && value != this.password.oldPayPwd;
                    }, message: '密码必须6位数字且不能与原支付密码相同', trigger: 'blur'
                }]
            }
        }
    },
    onReady() {
        this.$refs.uForm.setRules(this.rules);
    },
    methods: {
        submit() {
            this.$refs.uForm.validate(valid => {
                if (valid) {
                    const { oldPayPwd, payPwd } = this.password

                    changePayPwd({ oldPayPwd, payPwd }).then((res) => {
                        if (res.code == "00") {
                            uni.showToast({ title: '修改成功!',icon:'none' })

                            setTimeout(() => {
                                this.$Router.back(1);
                            }, 1000)
                        }
                    })
                }
            })

        }
    }
}
</script>

<style lang="less" scoped>
#changePayPsw {
    height: 100%;
    padding-top: 20rpx;
    > main {
        height: 100%;
        padding: 0 30rpx;
        background: white;
    }
}
</style>