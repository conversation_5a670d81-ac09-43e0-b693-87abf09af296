<template>
    <div id="setResetPayPsw">
        <main>
            <u-form :model="password" ref="uForm" :label-width="190">
                <u-form-item label="登录密码" prop="loginPwd">
                    <u-input type="password" :password-icon="false" v-model="password.loginPwd" placeholder="请输入登录密码" :clearable="false" />
                </u-form-item>
                <u-form-item label="支付密码" prop="payPwd">
                    <u-input type="password" :password-icon="false" v-model="password.payPwd" placeholder="请输入支付密码" :clearable="false" />
                </u-form-item>
                <u-form-item label="确认支付密码" prop="confirmPsw">
                    <u-input type="password" :password-icon="false" v-model="password.confirmPsw" placeholder="请确认支付密码" :clearable="false" />
                </u-form-item>
            </u-form>

            <view style="margin:150rpx 30rpx 30rpx">
                <u-button type="primary" @click="submit">提 交</u-button>
            </view>
        </main>
    </div>
</template>

<script>
import { setPayPwd } from "../../../http/api"

export default {
    name: "SetResetPayPsw",
    data() {
        return {
            password: {
                loginPwd: null,
                payPwd: null,
                confirmPsw: null
            },
            rules: {
                loginPwd: [{ required: true, min: 6, message: '登录密码必须6位及以上', trigger: ['change', 'blur'], }],
                payPwd: [{
                    required: true, pattern: /^[0-9]{6}$/, message: '密码必须6位数字', trigger: 'blur'
                }],
                confirmPsw: [{
                    required: true, validator: (rule, value, callback) => {
                        return /^\S{6,}$/.test(value) && value == this.password.payPwd;
                    }, message: '密码必须6位数字且与支付密码相同', trigger: 'blur'
                }]
            }
        }
    },
    onReady() {
        this.$refs.uForm.setRules(this.rules)
        uni.setNavigationBarTitle({ title: this.$Route.query.type == 0 ? '设置支付密码' : '找回支付密码' });
    },
    methods: {
        submit() {
            this.$refs.uForm.validate(valid => {
                if (valid) {
                    const { loginPwd, payPwd } = this.password

                    setPayPwd({ loginPwd, payPwd }).then((res) => {
                        if (res.code == "00") {
                            uni.showToast({ title: '支付密码设置成功!' ,icon:'none'})

                            setTimeout(() => {
                                this.$Router.back(1);
                            }, 1000)
                        }
                    })
                }
            })
        }
    }
}
</script>

<style lang="less" scoped>
#setResetPayPsw {
    height: 100%;
    padding-top: 20rpx;
    > main {
        height: 100%;
        padding: 0 30rpx;
        background: white;
    }
}
</style>