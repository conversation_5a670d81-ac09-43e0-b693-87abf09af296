<template>
    <div id="changeLoginPsw">
        <main>
            <u-form :model="user" ref="uForm">
                <u-form-item label="代理商编号" prop="agentCode" :label-width="170">
                    <u-input :value="user.agentCode" disabled />
                </u-form-item>
                <u-form-item label="旧密码" prop="oriPassword" :label-width="170">
                    <u-input type="password" v-model="user.oriPassword" placeholder="请输入旧密码" :clearable="false" />
                </u-form-item>
                <u-form-item label="新密码" prop="mdfPassword" :label-width="170">
                    <u-input type="password" v-model="user.mdfPassword" placeholder="请输入新密码" :clearable="false" />
                </u-form-item>

                <p class="tips">注：修改成功后需使用新密码登录！</p>
            </u-form>

            <view class="submit-btn">
                <u-button type="primary" @click="submit">提 交</u-button>
            </view>
        </main>
    </div>
</template>

<script>
import { updatePwd } from "../../../http/api"

export default {
    name: "ChangeLoginPsw",
    data() {
        return {
            user: {
                agentCode: this.$store.state.userInfo.agentCode,
                oriPassword: null,
                mdfPassword: null
            },
            rules: {
                oriPassword: [{ required: true, message: '请输入旧密码', trigger: ['change', 'blur'], }],
                mdfPassword: [{
                    required: true, validator: (rule, value, callback) => {
                        return /^\S{6,}$/.test(value) && value != this.user.oriPassword;
                    }, message: '密码必须6位及以上且不能与原密码相同', trigger: 'blur'
                }]
            }
        }
    },
    onReady() {
        this.$refs.uForm.setRules(this.rules)
    },
    methods: {
        validator(val) {
            return /^\S{6,}$/.test(val) && val != this.user.oriPassword;
        },
        submit() {
            this.$refs.uForm.validate(valid => {
                if (valid) {
                    updatePwd(this.user).then((res) => {
                        if (res.code == "00") {
                            uni.showToast({ title: '修改成功，请重新登录!', icon: 'none' })

                            setTimeout(() => {
                                this.$store.dispatch('login_out')
                            }, 1500);
                        }
                    })
                }
            })

        }
    }
}
</script>

<style lang="less" scoped>
#changeLoginPsw {
    height: 100%;
    padding-top: 20rpx;
    > main {
        height: 100%;
        padding: 0 30rpx;
        background: white;
        .tips {
            font-size: 24rpx;
            color: #ff9900;
            margin-top: 20rpx;
        }
        .submit-btn {
            margin: 150rpx 30rpx 30rpx;
        }
    }
}
</style>