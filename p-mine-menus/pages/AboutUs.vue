<template>
    <div id="aboutUs">
        <main>
            <image :src="appInfo.appLogoUrl" alt="" />
            <p>{{ appInfo.appName }}</p>
            <!--  #ifdef  APP-PLUS -->
            <p>版本号 {{ versionName }}</p>
            <!--  #endif -->
            <footer>
                <text>本产品由商互通开放平台提供技术支持</text>
            </footer>
        </main>
    </div>
</template>

<script>
export default {
    name: 'AboutUs',
    computed: {
        appInfo() {
            return this.$store.state.appInfo
        },
        versionName() {
            // #ifdef APP-PLUS
            return plus.runtime.version
            // #endif
            return '1.0.0'
        }
    }
}
</script>

<style lang="less" scoped>
#aboutUs {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    main {
        position: relative;
        > image {
            width: 148rpx;
            height: 148rpx;
            border-radius: 14rpx;
        }
        > p {
            margin: 20rpx 0;
            color: #555;
            text-align: center;
            &:first-of-type {
                font-weight: 500;
            }
        }
        footer {
            position: fixed;
            left: 0;
            text-align: center;
            bottom: 30rpx;
            width: 100%;
            color: #999;
            font-size: 22rpx;
        }
    }
}
</style>
