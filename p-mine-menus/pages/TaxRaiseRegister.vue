<template>
  <div id="taxRaiseRegister">
    <main>
      <p>身份证信息</p>
      <section class="file-info">
        <div>
          <p @click="upload(1)">
            <image class="upload" v-show="showFront" src="../../static/images/common/photo.png" alt="" />
            <span v-show="showFront">身份证人像面照片</span>
            <image class="img" v-show="!showFront" :src="'data:image/jpg;base64,' + idFrontImagePath" alt="" />
          </p>
          <p @click="upload(2)">
            <image class="upload" v-show="showReverse" src="../../static/images/common/photo.png" alt="" />
            <span v-show="showReverse">身份证国徽面照片</span>
            <image class="img" v-show="!showReverse" :src="'data:image/jpg;base64,' + idBackImagePath" alt="" />
          </p>
        </div>
      </section>
      <p></p>
      <section class="base-info">
        <p>
          <span>持卡人</span><span>{{ user.name }}</span>
        </p>
        <p>
          <span>身份证号码</span><span>{{ user.idNumber ? user.idNumber.replace(/(\d{3})\d*(\d{4})/, '$1 **** **** **** $2') : '' }}</span>
        </p>
      </section>
      <p></p>
      <u-form :model="user" ref="uForm" class="u-form" :label-width="160">
        <u-form-item label="银行卡号" prop="bankAccount">
          <u-input type="digit" placeholder="请输入银行卡号" v-model="user.bankAccount" />
        </u-form-item>
        <u-form-item label="手机号" prop="phone">
          <u-input type="tel" placeholder="请输入手机号" v-model="user.phone" />
        </u-form-item>
        <u-form-item label="短信验证码" prop="smsCode">
          <u-input type="digit" placeholder="请输入短信验证码" v-model="user.smsCode" />
          <u-button slot="right" size="mini" type="success" :disabled="time !== ''" @click="getSms">{{ time }}{{ smsMsg }}</u-button>
        </u-form-item>
        <p></p>
        <view class="custom-button">
          <button type="primary" @click="submit">注 册</button>
        </view>
      </u-form>
    </main>
  </div>
</template>

<script>
import { taxRegisterSms, taxRegister } from '../../http/api';

const rules = {
  bankAccount: [{ required: true, message: '必填' }],
  smsCode: [{ required: true, message: '必填' }],
  phone: [{ pattern: /^1[3456789]\d{9}$/, message: '手机号格式不正确' }]
};
export default {
  name: 'TaxRaiseRegister',
  data() {
    return {
      fileListone: [],
      fileListtwo: [],
      showFront: true,
      idFrontImagePath: '',
      showReverse: true,
      idBackImagePath: '',
      time: '',
      smsMsg: '获取验证码',
      user: {
        name: this.$store.state.userInfo.accountName,
        idNumber: this.$store.state.userInfo.idCardNo,
        bankAccount: '',
        phone: '',
        smsCode: ''
      }
    };
  },
  onLoad() {},
  onReady() {
    this.$refs.uForm.setRules(rules);
  },
  methods: {
    upload(num) {
      uni.chooseImage({
        count: 1,
        success: async res => {
          const base64Data = await this.pathToBase64(res.tempFilePaths[0]);
          if (base64Data) {
            const base64DataSplice = base64Data.split(',')[1];
            if (num == 1) {
              this.idFrontImagePath = base64DataSplice;
              this.showFront = false;
            } else {
              this.idBackImagePath = base64DataSplice;
              this.showReverse = false;
            }
          }
        }
      });
    },
    pathToBase64(path) {
      return new Promise((resolve, reject) => {
        if (typeof plus === 'object') {
          plus.io.resolveLocalFileSystemURL(
            this.getLocalFilePath(path),
            function (entry) {
              entry.file(
                function (file) {
                  var fileReader = new plus.io.FileReader();
                  fileReader.onload = function (data) {
                    resolve(data.target.result);
                  };
                  fileReader.onerror = function (error) {
                    reject(error);
                  };
                  fileReader.readAsDataURL(file);
                },
                function (error) {
                  reject(error);
                }
              );
            },
            function (error) {
              reject(error);
            }
          );
          return;
        }
        if (typeof wx === 'object' && wx.canIUse('getFileSystemManager')) {
          wx.getFileSystemManager().readFile({
            filePath: path,
            encoding: 'base64',
            success: function (res) {
              resolve('data:image/png;base64,' + res.data);
            },
            fail: function (error) {
              reject(error);
            }
          });
          return;
        }
        reject(new Error('not support'));
      });
    },
    getLocalFilePath(path) {
      if (
        path.indexOf('_www') === 0 ||
        path.indexOf('_doc') === 0 ||
        path.indexOf('_documents') === 0 ||
        path.indexOf('_downloads') === 0
      ) {
        return path;
      }
      if (path.indexOf('file://') === 0) {
        return path;
      }
      if (path.indexOf('/storage/emulated/0/') === 0) {
        return path;
      }
      if (path.indexOf('/') === 0) {
        var localFilePath = plus.io.convertAbsoluteFileSystem(path);
        if (localFilePath !== path) {
          return localFilePath;
        } else {
          path = path.substr(1);
        }
      }
      return '_www/' + path;
    },
    getSms() {
      if (this.smsMsg == '获取验证码') {
        if (
          this.idFrontImagePath != '' &&
          this.idBackImagePath != '' &&
          this.user.bankAccount != '' &&
          /^1[3456789]\d{9}$/.test(this.user.phone)
        ) {
          taxRegisterSms({
            name: this.user.name,
            idNumber: this.user.idNumber,
            idFrontImagePath: this.idFrontImagePath,
            idBackImagePath: this.idBackImagePath,
            bankAccount: this.user.bankAccount,
            phone: this.user.phone
          }).then(res => {
            if (res.code == '00') {
              if (!res.data) {
                this.time = 60;
                this.smsMsg = 's后获取';
                var timer = setInterval(() => {
                  this.time--;
                  if (this.time <= 0) {
                    this.time = '';
                    this.smsMsg = '获取验证码';
                    clearInterval(timer);
                  }
                }, 1000);
              } else {
                uni.showToast({
                  title: '当前身份信息已注册，可跳过本次注册！',
                  icon: 'none'
                });
                this.$store.commit('SET_USERINFO', {
                  agentCode: this.$store.state.userInfo.agentCode,
                  realName: this.$store.state.userInfo.name,
                  accountName: this.$store.state.userInfo.name,
                  agentLevel: this.$store.state.userInfo.agentLevel,
                  mobile: this.$store.state.userInfo.mobile,
                  payMarketMode: this.$store.state.userInfo.payMarketMode,
                  companyName: this.$store.state.userInfo.companyName,
                  appAuthStatus: this.$store.state.userInfo.appAuthStatus,
                  payTaxType: this.$store.state.userInfo.payTaxType,
                  taxationRegisterStatus: 1,
                  idCardNo: this.$store.state.userInfo.idCardNo,
                  showCreditDiscountOpenConf: this.$store.state.userInfo.showCreditDiscountOpenConf,
                  showSecTransOpenConf: this.$store.state.userInfo.showSecTransOpenConf,
                  memberLevel: this.$store.state.userInfo.memberLevel,
                  isMobileVerify: this.$store.state.userInfo.isMobileVerify,
                  loginName: this.$store.state.userInfo.loginName,
                  bankCardNo: this.$store.state.userInfo.bankCardNo,
                  bankName: this.$store.state.userInfo.bankName,
                  payChannelCode: this.$store.state.userInfo.payChannelCode,
                  cloudOpenStatus: this.$store.state.userInfo.cloudOpenStatus,
                  wsyDisplaySwitch: this.$store.state.userInfo.wsyDisplaySwitch,
                  dlgSignStatue: this.$store.state.userInfo.dlgSignStatue,
                  hkpaySignStatue: this.$store.state.userInfo.hkpaySignStatue,
                  receiveType: this.$store.state.userInfo.receiveType,
                  email: this.$store.state.userInfo.email
                });
                setTimeout(() => {
                  this.$Router.back(1);
                }, 1500);
              }
            }
          });
        } else {
          uni.showModal({
            showCancel: false,
            content: '请确认身份证正反面、银行卡号、手机号等信息是否填写完整且符合要求！'
          });
        }
      }
    },
    submit() {
      this.$refs.uForm.validate(valid => {
        if (valid) {
          taxRegister(this.user).then(res => {
            if (res.code == '00') {
              uni.showToast({
                title: res.message,
                icon: 'none'
              });
              this.$store.commit('SET_USERINFO', {
                agentCode: this.$store.state.userInfo.agentCode,
                realName: this.$store.state.userInfo.realName,
                accountName: this.$store.state.userInfo.accountName,
                agentLevel: this.$store.state.userInfo.agentLevel,
                mobile: this.$store.state.userInfo.mobile,
                companyName: this.$store.state.userInfo.companyName,
                appAuthStatus: this.$store.state.userInfo.appAuthStatus,
                payTaxType: this.$store.state.userInfo.payTaxType,
                taxationRegisterStatus: 1,
                idCardNo: this.$store.state.userInfo.idCardNo,
                showCreditDiscountOpenConf: this.$store.state.userInfo.showCreditDiscountOpenConf,
                showSecTransOpenConf: this.$store.state.userInfo.showSecTransOpenConf,
                memberLevel: this.$store.state.userInfo.memberLevel,
                isMobileVerify: this.$store.state.userInfo.isMobileVerify,
                loginName: this.$store.state.userInfo.loginName,
                bankCardNo: this.$store.state.userInfo.bankCardNo,
                bankName: this.$store.state.userInfo.bankName,
                payChannelCode: this.$store.state.userInfo.payChannelCode,
                cloudOpenStatus: this.$store.state.userInfo.cloudOpenStatus,
                wsyDisplaySwitch: this.$store.state.userInfo.wsyDisplaySwitch,
                dlgSignStatue: this.$store.state.userInfo.dlgSignStatue,
                hkpaySignStatue: this.$store.state.userInfo.hkpaySignStatue,
                receiveType: this.$store.state.userInfo.receiveType,
                email: this.$store.state.userInfo.email
              });
              setTimeout(() => {
                this.$Router.back(1);
              }, 1500);
            }
          });
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
#taxRaiseRegister {
  height: 100%;
  padding-bottom: 100rpx;
  main {
    background-color: #fff;
    > p {
      background: #f8f8f8;
      margin: 0;
      &:nth-of-type(1) {
        line-height: 78rpx;
        color: #999999;
        padding-left: 30rpx;
      }
      &:not(:first-of-type) {
        height: 20rpx;
      }
    }
    > section {
      margin-bottom: 20rpx;
    }
    .base-info {
      > p {
        line-height: 88rpx;
        margin: 0 0 0 30rpx;
        position: relative;
        &:nth-of-type(1) {
          border-bottom: 2rpx solid #e6e6e6;
        }
        > span {
          &:nth-of-type(1) {
            width: 142rpx;
            color: #666666;
            margin-right: 40rpx;
            display: inline-block;
          }
          &:nth-of-type(2) {
            color: #222222;
          }
        }
      }
    }
    .file-info {
      padding: 30rpx;
      > div {
        width: 690rpx;
        background: #f3f5f7;
        border-radius: 24rpx;
        display: flex;
        > p {
          width: 316rpx;
          height: 200rpx;
          text-align: center;
          border: 4rpx dashed #666666;
          border-radius: 12rpx;
          margin: 30rpx 0;
          position: relative;
          &:nth-of-type(1) {
            margin: 30rpx 18rpx 30rpx 20rpx;
          }
          .upload {
            width: 88rpx;
            height: 88rpx;
            margin: 46rpx 0 6rpx;
          }
          > span {
            font-size: 18rpx;
            color: #222222;
            display: block;
          }
          .img {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
          }
        }
      }
    }
    /deep/ .u-form {
      .u-form-item {
        padding: 20rpx 30rpx;
      }
    }
    .custom-button {
      position: fixed;
      bottom: 0;
    }
  }
}
</style>
