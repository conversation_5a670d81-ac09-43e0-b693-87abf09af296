<template>
    <div id="myBankCard">
        <main>
            <div>
                <template v-if="userInfo.payChannelCode === '1006'">
                    <section>
                        <div>
                            <image src="../static/images/zfb.png" alt="" />
                        </div>
                        <p>
                            <span>支付宝账号</span>
                        </p>
                        <span @click="change"> 更换账号 </span>
                    </section>
                    <p>{{ userInfo.receiveType === 1 ? userInfo.email : userInfo.mobile }}</p>
                </template>
                <template v-else>
                    <section>
                        <div>
                            <image src="../static/images/bank.png" alt="" />
                        </div>

                        <p>
                            <span>{{ bankCard.bankName }}</span>
                            <span>储蓄卡</span>
                        </p>
                        <image src="../static/images/changeBankCard.png" alt="" @click="change" />
                    </section>
                    <p>{{ bankCard.bankCardNoMask.replace(/(\d{4})\d*(\d{4})/, '$1 **** **** $2') }}</p>
                </template>
            </div>
        </main>
    </div>
</template>

<script>
import { getSettleCard } from '../../http/api'

export default {
    name: 'MyBankCard',
    data() {
        return {
            bankCard: {
                bankName: '',
                bankCardNoMask: ''
            }
        }
    },
    computed: {
        userInfo() {
            return this.$store.state.userInfo
        }
    },
    onShow() {
        if (this.userInfo.payChannelCode === '1006') return
        getSettleCard().then(res => {
            if (res.code == '00') {
                this.bankCard = {
                    bankName: res.data.bankName,
                    bankCardNoMask: res.data.bankCardNoMask
                }
            }
        })
    },
    methods: {
        change() {
            if (this.$store.state.userInfo.accountName != null && this.$store.state.userInfo.idCardNo != null) {
                if(this.userInfo.payChannelCode === '1009' && this.userInfo.hkpaySignStatue === 2){
                     this.$Router.push({
                    name: 'ChangeBankCardSync'
                })
                return
                }
                this.$Router.push({
                    name: 'ChangeBankCard'
                })
            } else {
                uni.showToast({
                    title: '结算信息及个人信息不完整！',
                    icon: 'none'
                })
            }
        }
    }
}
</script>

<style lang="less" scoped>
#myBankCard {
    > main {
        padding: 20rpx 30rpx;

        > div {
            color: white;
            background-image: url('../static/images/bankCardBgImg.png');
            background-size: 100% 100%;

            > section {
                display: flex;
                position: relative;

                > div {
                    width: 68rpx;
                    height: 68rpx;
                    background-color: white;
                    border-radius: 50%;
                    margin: 62rpx 24rpx 0 50rpx;

                    > image {
                        width: 44rpx;
                        height: 44rpx;
                        margin: 12rpx;
                    }
                }

                > p {
                    margin: 0;

                    > span {
                        display: block;

                        &:nth-of-type(1) {
                            font-size: 36rpx;
                            font-weight: 600;
                            margin: 60rpx 0 6rpx;
                        }

                        &:nth-of-type(2) {
                            font-size: 24rpx;
                        }
                    }
                }

                > image {
                    width: 158rpx;
                    height: 48rpx;
                    position: absolute;
                    top: 60rpx;
                    right: 50rpx;
                }

                > span {
                    width: 158rpx;
                    height: 48rpx;
                    position: absolute;
                    top: 60rpx;
                    right: 50rpx;
                    border: 1px solid #fff;
                    border-radius: 8rpx;
                    font-size: 24rpx;
                    text-align: center;
                    line-height: 48rpx;
                }
            }

            > p {
                font-size: 36rpx;
                font-weight: 600;
                padding: 60rpx 0 48rpx 142rpx;
                margin: 0;
            }
        }
    }
}
</style>
