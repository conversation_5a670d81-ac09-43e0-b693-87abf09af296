<template>
  <div id="share">
    <main>
      <view class="tabs">
        <u-tabs :list="tabList" :bar-width="60" :is-scroll="false" :current="active" @change="getTab" />
        <view class="right-tab-mask" v-show="active == 1" @click="getTab(1)" />
      </view>

      <view class="u-dropdown">
        <u-dropdown ref="materials" @close="showMaterials = false">
          <u-dropdown-item v-model="originTypeTag" title="朋友圈素材" :options="materials" @change="getMaterials" />
        </u-dropdown>
      </view>
      <!-- 分享注册 -->
      <div v-show="active == 0 && show" class="qr-swiper">
        <swiper>
          <swiper-item v-for="(i, index) in imgArr" :key="index">
            <div @longpress="openAppToolBox(i.cImg)">
              <create-qrcode
                v-if="active == 0 && show && value != '' && i.cImg == ''"
                :cid="'qrcard' + index"
                loadMake
                :val="value"
                :show="false"
                :showLoading="false"
                @result="path => draw(path, index)"
              />
              <image :src="i.cImg" mode="widthFix" show-menu-by-longpress />
              <canvas v-if="!i.cImg" style="width: 100%; height: 100%" :canvas-id="'shareCanvas' + index" :id="'shareCanvas' + index"></canvas>
            </div>
          </swiper-item>
        </swiper>
      </div>
      <!-- 素材 -->
      <section v-show="active == 1">
        <swiper @change="onMaterialChange" class="material-swiper">
          <swiper-item v-for="(m, index) in materialImgs" :key="index">
            <image :ref="m.ref" :src="m.img" alt="" @longpress="downloadToSaveImg(m.img)" show-menu-by-longpress />
          </swiper-item>
        </swiper>
      </section>
      <view v-if="imgArr[0].cImg" class="tips"> 长按可保存、分享 </view>
    </main>
  </div>
</template>

<script>
import { getTagType, getTagImgs } from '../../http/api'
import CreateQrcode from 'tki-qrcode'

export default {
  name: 'Share',
  components: { CreateQrcode },
  data() {
    return {
      active: 0,
      originTypeTag: 3,
      showMaterials: false,
      materials: [],
      show: true,
      value: '',
      imgArr: [
        { lImg: require('../static/images/share5.png'), cImg: '' },
        { lImg: require('../static/images/share4.png'), cImg: '' },
        { lImg: require('../static/images/share1.png'), cImg: '' },
        { lImg: require('../static/images/share2.png'), cImg: '' },
        { lImg: require('../static/images/share3.png'), cImg: '' }
      ],
      tabList: [{ name: '分享注册' }, { name: '朋友圈素材' }],
      materialImgs: [],
      materiaIndex: 0
    }
  },
  onLoad() {
    if (this.$store.state.userInfo.agentLevel == 10) {
      this.show = false
      uni.showToast({
        title: '抱歉，暂时不能分享发展服务商！',
        duration: 2000,
        icon: 'none'
      })
    } else {
      this.show = true
      this.value = `${this.$store.state.appInfo.registerPageUrl}?agentCode=${this.$store.state.userInfo.agentCode}&payMarketMode=${this.$store.state.currentMode}&number=${Math.random()}`
    }
  },
  onReady: function (e) {},
  methods: {
    draw(path, index) {
      const ctx = uni.createCanvasContext('shareCanvas' + index)

      //背景
      ctx.drawImage(this.imgArr[index].lImg, 0, 0, uni.upx2px(540), uni.upx2px(800))
      //二维码
      ctx.setFillStyle('#fff')
      ctx.fillRect(uni.upx2px(166), uni.upx2px(536), uni.upx2px(208), uni.upx2px(208))
      ctx.drawImage(path, uni.upx2px(170), uni.upx2px(540), uni.upx2px(200), uni.upx2px(200))
      //文字
      ctx.setFontSize(uni.upx2px(24))
      ctx.setFillStyle('white')
      ctx.setTextAlign('center')
      ctx.fillText('识别二维码注册即成代理商', uni.upx2px(270), uni.upx2px(780))
      ctx.draw(false, () => {
        uni.canvasToTempFilePath({
          x: 0,
          y: 0,
          width: uni.upx2px(540),
          height: uni.upx2px(800),
          canvasId: 'shareCanvas' + index,
          success: res => {
            this.imgArr[index].cImg = res.tempFilePath
          },
          fail: err => {
            console.log(err)
          }
        })
      })
    },
    getTab(index) {
      this.active = index
      if (index == 1) {
        if (!this.showMaterials) {
          // 打开菜单栏
          getTagType().then(res => {
            if (res.code == '00') {
              this.materials = []
              res.data.forEach(i => {
                this.materials.push({
                  label: i.tagName,
                  value: i.originTypeTag
                })
              })
              if (res.data.findIndex(i => i.originTypeTag == this.originTypeTag) == -1) {
                this.originTypeTag = res.data[0].originTypeTag
              }
              if (this.materialImgs.length == 0) {
                getTagImgs({ originType: 3, originTypeTag: this.originTypeTag }).then(res => {
                  if (res.code == '00') {
                    res.data.forEach((i, index) => {
                      this.materialImgs.push({
                        ref: 'material' + (index + 1),
                        img: i.imgUrl
                      })
                    })
                  }
                })
              }
              this.$refs.materials.open(0)
              this.showMaterials = true
            }
          })
        } else {
          // 关闭菜单栏
          this.$refs.materials.close()
        }
      } else {
        this.$refs.materials.close()
      }
    },
    onMaterialChange(index) {
      this.materiaIndex = index
    },
    getMaterials(value) {
      getTagImgs({ originType: 3, originTypeTag: value }).then(res => {
        if (res.code == '00') {
          this.materialImgs = []
          res.data.forEach((i, index) => {
            this.materialImgs.push({
              ref: 'material' + (index + 1),
              img: i.imgUrl
            })
          })
        }
      })
    },
    downloadToSaveImg(imgNetworkUrl) {
      // #ifdef APP-PLUS
      uni.getImageInfo({
        src: imgNetworkUrl,
        success: ({ path }) => {
          this.openAppToolBox(path)
        }
      })
      // #endif
    },
    openAppToolBox(imgPath) {
      // #ifdef APP-PLUS
      uni.showActionSheet({
        itemList: ['保存', '分享'],
        success: function ({ tapIndex }) {
          switch (tapIndex) {
            case 0:
              uni.saveImageToPhotosAlbum({
                filePath: imgPath,
                success: function () {
                  uni.showToast({ title: '图片保存成功' })
                }
              })
              break
            case 1:
              plus.share.sendWithSystem(
                { pictures: [imgPath] },
                function () {
                  console.log('分享成功')
                },
                function (e) {
                  console.log(e)
                }
              )
              break
            default:
              break
          }
        }
      })
      // #endif
    }
  }
}
</script>

<style lang="less" scoped>
#share {
  > main {
    .qr-swiper {
      swiper {
        overflow: visible;
        height: 880upx;
        swiper-item {
          margin-top: 80upx;
          > div {
            width: 540upx;
            height: 800upx;
            margin: 0 auto;
            > image {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
    /deep/ .material-swiper {
      height: 880upx;
      text-align: center;
      swiper-item {
        margin-top: 80upx;
        image {
          width: 540upx;
          height: 800upx;
        }
      }
    }
    .tabs {
      position: relative;
      z-index: 99;
      .right-tab-mask {
        position: absolute;
        top: 0;
        right: 0;
        height: 80rpx;
        width: 50%;
        opacity: 0;
      }
    }
    .tips {
      margin-top: 80rpx;
      color: #999;
      text-align: center;
      font-size: 26rpx;
    }
    .u-dropdown {
      margin-top: -80rpx;
    }
  }
}
</style>