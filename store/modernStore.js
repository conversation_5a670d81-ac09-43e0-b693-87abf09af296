/**
 * 现代化状态管理器
 * 基于Vuex的增强版状态管理，支持模块化、持久化、中间件等功能
 */

import { storage } from '../static/utils/modernUtils.js'

// 状态持久化中间件
const persistenceMiddleware = (store) => {
  // 从存储中恢复状态
  const savedState = storage.get('vuex_state')
  if (savedState) {
    store.replaceState({
      ...store.state,
      ...savedState
    })
  }
  
  // 监听状态变化并持久化
  store.subscribe((mutation, state) => {
    // 只持久化需要的状态
    const persistedState = {
      userInfo: state.userInfo,
      token: state.token,
      currentMode: state.currentMode,
      appInfo: state.appInfo,
      settings: state.settings
    }
    storage.set('vuex_state', persistedState)
  })
}

// 日志中间件
const loggerMiddleware = (store) => {
  store.subscribe((mutation, state) => {
    if (process.env.NODE_ENV === 'development') {
      console.group(`🔄 ${mutation.type}`)
      console.log('Payload:', mutation.payload)
      console.log('State:', state)
      console.groupEnd()
    }
  })
}

// 用户模块
const userModule = {
  namespaced: true,
  
  state: {
    userInfo: {},
    token: '',
    isLoggedIn: false,
    permissions: [],
    preferences: {
      theme: 'light',
      language: 'zh-CN',
      notifications: true
    }
  },
  
  getters: {
    isAuthenticated: state => !!state.token && !!state.userInfo.id,
    userRole: state => state.userInfo.role || 'guest',
    hasPermission: state => permission => state.permissions.includes(permission),
    displayName: state => state.userInfo.realName || state.userInfo.loginName || '用户',
    avatar: state => state.userInfo.avatar || '/static/images/default-avatar.png'
  },
  
  mutations: {
    SET_USER_INFO(state, userInfo) {
      state.userInfo = { ...state.userInfo, ...userInfo }
    },
    
    SET_TOKEN(state, token) {
      state.token = token
      state.isLoggedIn = !!token
    },
    
    SET_PERMISSIONS(state, permissions) {
      state.permissions = permissions || []
    },
    
    SET_PREFERENCES(state, preferences) {
      state.preferences = { ...state.preferences, ...preferences }
    },
    
    CLEAR_USER_DATA(state) {
      state.userInfo = {}
      state.token = ''
      state.isLoggedIn = false
      state.permissions = []
    }
  },
  
  actions: {
    async login({ commit, dispatch }, { username, password }) {
      try {
        // 这里应该调用登录API
        const response = await api.login({ username, password })
        
        commit('SET_TOKEN', response.token)
        commit('SET_USER_INFO', response.userInfo)
        commit('SET_PERMISSIONS', response.permissions)
        
        // 获取用户详细信息
        await dispatch('fetchUserInfo')
        
        return response
      } catch (error) {
        throw error
      }
    },
    
    async logout({ commit }) {
      try {
        // 调用登出API
        await api.logout()
      } catch (error) {
        console.error('Logout API failed:', error)
      } finally {
        commit('CLEAR_USER_DATA')
        storage.remove('token')
        
        // 跳转到登录页
        uni.reLaunch({
          url: '/pages/login/Login'
        })
      }
    },
    
    async fetchUserInfo({ commit, state }) {
      if (!state.token) return
      
      try {
        const userInfo = await api.getUserInfo()
        commit('SET_USER_INFO', userInfo)
        return userInfo
      } catch (error) {
        console.error('Fetch user info failed:', error)
        throw error
      }
    },
    
    async updateProfile({ commit }, profileData) {
      try {
        const updatedInfo = await api.updateProfile(profileData)
        commit('SET_USER_INFO', updatedInfo)
        return updatedInfo
      } catch (error) {
        throw error
      }
    },
    
    updatePreferences({ commit }, preferences) {
      commit('SET_PREFERENCES', preferences)
    }
  }
}

// 应用模块
const appModule = {
  namespaced: true,
  
  state: {
    appInfo: {},
    currentMode: 1, // 1: 渠道模式, 2: 直营模式
    isIosCheckPass: 1,
    appNotifications: [],
    systemConfig: {},
    loading: false,
    networkStatus: {
      isConnected: true,
      networkType: 'wifi'
    }
  },
  
  getters: {
    unreadNotifications: state => state.appNotifications.filter(n => n.readingSatus === 0),
    unreadCount: (state, getters) => getters.unreadNotifications.length,
    isOnline: state => state.networkStatus.isConnected
  },
  
  mutations: {
    SET_APP_INFO(state, appInfo) {
      state.appInfo = { ...state.appInfo, ...appInfo }
    },
    
    SET_CURRENT_MODE(state, mode) {
      state.currentMode = mode
    },
    
    SET_IOS_CHECK_PASS(state, status) {
      state.isIosCheckPass = status
    },
    
    SET_APP_NOTIFICATIONS(state, notifications) {
      state.appNotifications = notifications || []
    },
    
    UPDATE_NOTIFICATION_STATUS(state, { id, status }) {
      const notification = state.appNotifications.find(n => n.id === id)
      if (notification) {
        notification.readingSatus = status
      }
    },
    
    SET_SYSTEM_CONFIG(state, config) {
      state.systemConfig = { ...state.systemConfig, ...config }
    },
    
    SET_LOADING(state, loading) {
      state.loading = loading
    },
    
    SET_NETWORK_STATUS(state, status) {
      state.networkStatus = { ...state.networkStatus, ...status }
    }
  },
  
  actions: {
    async initApp({ commit, dispatch }) {
      try {
        commit('SET_LOADING', true)
        
        // 并行获取应用信息
        const [appInfo, systemConfig] = await Promise.all([
          api.getAppInfo(),
          api.getSystemConfig()
        ])
        
        commit('SET_APP_INFO', appInfo)
        commit('SET_SYSTEM_CONFIG', systemConfig)
        
        // 获取通知
        await dispatch('fetchNotifications')
        
      } catch (error) {
        console.error('Init app failed:', error)
      } finally {
        commit('SET_LOADING', false)
      }
    },
    
    async fetchNotifications({ commit }) {
      try {
        const notifications = await api.getNotifications()
        commit('SET_APP_NOTIFICATIONS', notifications)
      } catch (error) {
        console.error('Fetch notifications failed:', error)
      }
    },
    
    async markNotificationAsRead({ commit }, notificationId) {
      try {
        await api.markNotificationAsRead(notificationId)
        commit('UPDATE_NOTIFICATION_STATUS', { id: notificationId, status: 1 })
      } catch (error) {
        console.error('Mark notification as read failed:', error)
      }
    },
    
    switchMode({ commit }, mode) {
      commit('SET_CURRENT_MODE', mode)
    },
    
    updateNetworkStatus({ commit }, status) {
      commit('SET_NETWORK_STATUS', status)
    }
  }
}

// 业务数据模块
const businessModule = {
  namespaced: true,
  
  state: {
    wallet: {
      todayProfitAmount: 0,
      todayTransAmount: 0,
      monthOneDirectTxnAmt: 0,
      monthOneTeamTxnAmt: 0
    },
    statistics: {
      charts: [],
      rankings: []
    },
    cache: new Map()
  },
  
  getters: {
    formattedWallet: state => {
      const { wallet } = state
      return {
        todayProfit: (wallet.todayProfitAmount || 0).toFixed(2),
        todayTrans: (wallet.todayTransAmount || 0).toFixed(2),
        monthDirect: (wallet.monthOneDirectTxnAmt || 0).toFixed(2),
        monthTeam: (wallet.monthOneTeamTxnAmt || 0).toFixed(2)
      }
    }
  },
  
  mutations: {
    SET_WALLET_DATA(state, walletData) {
      state.wallet = { ...state.wallet, ...walletData }
    },
    
    SET_STATISTICS_DATA(state, { type, data }) {
      state.statistics[type] = data
    },
    
    SET_CACHE(state, { key, data }) {
      state.cache.set(key, {
        data,
        timestamp: Date.now()
      })
    },
    
    CLEAR_CACHE(state) {
      state.cache.clear()
    }
  },
  
  actions: {
    async fetchWalletData({ commit }) {
      try {
        const walletData = await api.getWalletData()
        commit('SET_WALLET_DATA', walletData)
        return walletData
      } catch (error) {
        console.error('Fetch wallet data failed:', error)
        throw error
      }
    },
    
    async fetchStatistics({ commit }, type) {
      try {
        const data = await api.getStatistics(type)
        commit('SET_STATISTICS_DATA', { type, data })
        return data
      } catch (error) {
        console.error('Fetch statistics failed:', error)
        throw error
      }
    },
    
    clearBusinessCache({ commit }) {
      commit('CLEAR_CACHE')
    }
  }
}

// 创建store配置
const storeConfig = {
  modules: {
    user: userModule,
    app: appModule,
    business: businessModule
  },
  
  plugins: [
    persistenceMiddleware,
    loggerMiddleware
  ],
  
  strict: process.env.NODE_ENV === 'development'
}

export default storeConfig
