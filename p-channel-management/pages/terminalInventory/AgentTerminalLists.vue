<template>
    <div id="agentTerminalLists">
        <main>
            <div class="search-bar">
                <view class="content">
                    <u-field :label-width="50" icon="search" :border-bottom="false" type="text" placeholder="请输入终端号" v-model="terminalNo" />
                </view>
            </div>

            <view class="tabs">
                <u-tabs :list="tabList" :height="70" :current="active" :show-bar="false" @change="change" />
            </view>

            <section>
                <no-content v-if="show" />

                <section v-show="!show">
                    <p v-for="t in terminals" :key="t" @click="terminalDetail(t)">
                        <span>终端物理号：</span>
                        <span>{{ t }}</span>
                    </p>
                </section>
            </section>
        </main>
    </div>
</template>

<script>
import { getAgentTerminalList } from "../../../http/api"
import { debounce } from '../../../static/utils/date'

export default {
    name: "AgentTerminalLists",
    data() {
        return {
            active: 0,
            typeCount: {
                notBoundCount: 0,
                boundCount: 0,
                inactivatedCount: 0,
                activityCount: 0,
                standerCount: 0,
            },
            terminalNo: "",
            show: false,
            terminals: []
        };
    },
    computed: {
        tabList() {
            const { notBoundCount, boundCount, inactivatedCount, activityCount, standerCount } = this.typeCount
            const arr = [
                { name: '未绑定(' + notBoundCount + ')' },
                { name: '已绑定(' + boundCount + ')' },
                { name: '未激活(' + inactivatedCount + ')' },
                { name: '已激活(' + activityCount + ')' },
                { name: '已达标(' + standerCount + ')' }
            ]
            return arr
        }
    },
    onLoad() {
        this.getAgentTerminalList(this.$Route.query.agentCode, this.$Route.query.model, 1, this.terminalNo);
    },
    watch: {
        terminalNo: debounce(function () {
            this.search();
        }, 700)
    },
    methods: {
        getAgentTerminalList(agentCode, model, status, terminalNo) {
            getAgentTerminalList({
                agentCode: agentCode,
                model: model,
                status: status,
                terminalNo: terminalNo,
            }).then((res) => {
                if (res.code == "00") {
                    if (res.data.terminalNo.length != 0) {
                        this.show = false;
                        this.typeCount = res.data.typeCount;
                        this.terminals = res.data.terminalNo;
                        uni.pageScrollTo({ scrollTop: 0, duration: 300 });
                    } else {
                        this.show = true;
                    }
                }
            });
        },
        change(index) {
            if (index != null) {
                this.active = index
            }
            var active =
                this.active == 2
                    ? 5
                    : this.active == 3 || this.active == 4
                        ? this.active
                        : this.active + 1;
            this.getAgentTerminalList(this.$Route.query.agentCode, this.$Route.query.model, active, this.terminalNo);
        },

        search() {
            if (!this.$Route.query.model) return;
            this.change(null)
        },
        terminalDetail(terminalNo) {
            this.$Router.push({ name: "TerminalDetail", params: { agentCode: this.$Route.query.agentCode, terminalNo: terminalNo } });
        }
    }
};
</script>

<style lang="less" scoped>
#agentTerminalLists {
    height: 100%;
    main {
        height: 100%;
        background-color: #fff;
        position: relative;
        overflow: hidden;
        .tabs {
            margin-top: 20rpx;
            height: 90rpx;
            padding: 0 20rpx;
            /deep/ .u-tabs {
                border-radius: 10rpx;
                background: #f3f5f7 !important ;
            }
        }
        > section {
            > section {
                height: calc(100vh - 220rpx);
                overflow-y: scroll;
                p {
                    margin: 0 30rpx;
                    padding: 30rpx 0;
                    background-color: white;
                    border-bottom: 2rpx solid #e5e5e5;
                    span:first-child {
                        color: #666666;
                    }
                }
            }
        }
    }
}
</style>