<template>
    <div id="terminalInventory">
        <main :class="direct == 0 ? 'p-b-100':''">
                <!-- <view class="top-navbar">
                    <text @click="rules">分润规则</text>
                </view> -->

            <no-content v-if="show" />

            <section v-if="!show">
                <div v-for="(t, index) in terminals" :key="index" @click="inventoryDetails(t.model)">
                    <image src="@/static/images/home/<USER>" alt="" />
                    <div class="right">
                        <div>
                            <p>
                                机具型号：<span> {{ t.model }}</span>
                            </p>
                            <p>
                                品牌名称：<span>{{ t.brandName }}</span>
                            </p>
                        </div>
                        <p>{{ t.count }}台</p>
                    </div>
                </div>
            </section>
        </main>

        <footer v-if="direct == '0'" class="custom-button">
            <button @click="retrace">回 撤</button>
        </footer>
    </div>
</template>

<script>
import { gatTerminals } from "../../../http/api"

export default {
    name: "TerminalInventory",
    data() {
        return {
            show: false,
            terminals: [],
            direct: ''
        };
    },
    onLoad() {
        this.direct = this.$Route.query.direct
    },
    onShow() {
        gatTerminals(
            this.$Route.query.agentCode || ""
        ).then((res) => {
            if (res.code == "00") {
                if (res.data.length != 0) {
                    this.terminals = res.data;
                } else {
                    this.show = true;
                }
            }
        });
    },
    methods: {
        rules() {
            this.$Router.push({ name: 'Rules', params: { agentCode: this.$Route.query.agentCode } });
        },
        inventoryDetails(model) {
            this.$Router.push({ name: 'AgentTerminalLists', params: { model, agentCode: this.$Route.query.agentCode } })
        },
        retrace() {
            this.$Router.push({ name: "Retrace", params: { agentCode: this.$Route.query.agentCode } });
        },
    },
};
</script>

<style lang="less" scoped>
#terminalInventory {
    height: 100%;
    main {
        min-height: 100%;
        background-color: #fff;
        .top-navbar {
            padding: 20rpx 30rpx;
            background-color: #f3f5f7;
            text-align: right;
            color: #004ea9;
            font-size: 26rpx;
        }
        > section {
            > div {
                display: flex;
                align-items: center;
                > image {
                    width: 68rpx;
                    height: 68rpx;
                    margin: 0 0 14rpx 30rpx;
                }
                > .right {
                    flex: 1;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    border-bottom: 2rpx solid #e5e5e5;
                    padding: 20rpx 0;
                    margin-left: 20rpx;
                    > div {
                        p {
                            &:first-child {
                                > span {
                                    font-weight: bold;
                                }
                            }
                            &:last-child {
                                margin: 6rpx 0 14rpx;
                                font-size: 24rpx;
                                color: #666666;
                            }
                        }
                    }
                    > p {
                        flex-shrink: 0;
                        margin-right: 30rpx;
                        color: #222222;
                    }
                }
                p {
                    margin: 0;
                    padding: 0;
                }
            }
        }
    }
    .custom-button {
        position: fixed;
        bottom: 0;
    }
    .p-b-100 {
        padding-bottom: 100rpx;
    }
}
</style>