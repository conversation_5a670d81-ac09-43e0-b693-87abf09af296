<template>
    <div id="rules">
        <main>
            <div>
                <view class="header">
                    <p><span class="span"></span><span>结算政策</span></p>
                    <u-tag v-if="!readonly" @click="addSettlePrice" text="新增模板" shape="circleLeft" />
                </view>
                <u-field label="政策类型" label-width="200" placeholder="请选择政策类型" v-model="settlePrice" :disabled="this.$store.state.userInfo.agentLevel != 1">
                    <u-icon @click="showSettlePrices" slot="right" name="search" size="36" />
                </u-field>
                <u-select v-model="settlePricesPicker" mode="single-column" :list="settlePrices" value-name="rateType" label-name="text" @confirm="settlePriceConfirm" />

                <u-form :model="cost" ref="uForm" class="u-form">
                    <u-form-item label="借记卡(%)" prop="debitCost">
                        <u-input type="digit" v-model="cost.debitCost" :disabled="readonly" :clearable="false" placeholder-style="font-size:24rpx" />
                    </u-form-item>
                    <u-form-item label="借记卡封顶值(元)" prop="debitCapValue">
                        <u-input type="digit" v-model="cost.debitCapValue" :disabled="readonly" :clearable="false" placeholder-style="font-size:24rpx" />
                    </u-form-item>
                    <u-form-item label="信用卡(%)" prop="creditCost">
                        <u-input type="digit" v-model="cost.creditCost" :disabled="readonly" :clearable="false" placeholder-style="font-size:24rpx" />
                    </u-form-item>
                    <u-form-item v-if="creditDiscountOpenConf" label="信用卡特惠(%)" prop="creditDiscountCost">
                        <u-input type="digit" v-model="cost.creditDiscountCost" :disabled="readonly" :clearable="false" placeholder-style="font-size:24rpx" />
                    </u-form-item>
                    <u-form-item v-if="secTransOpenConf" label="信用卡秒到(%)" prop="creditSecTransCost">
                        <u-input type="digit" v-model="cost.creditSecTransCost" :disabled="readonly" :clearable="false" placeholder-style="font-size:24rpx" />
                    </u-form-item>
                    <u-form-item label="扫码(%)" prop="scanCost">
                        <u-input type="digit" v-model="cost.scanCost" :disabled="readonly" :clearable="false" placeholder-style="font-size:24rpx" />
                    </u-form-item>
                    <u-form-item label="闪付(%)" prop="passCost">
                        <u-input type="digit" v-model="cost.passCost" :disabled="readonly" :clearable="false" placeholder-style="font-size:24rpx" />
                    </u-form-item>

                    <footer v-if="!readonly" class="custom-button">
                        <u-button type="primary" @click="submit">提交</u-button>
                    </footer>
                </u-form>
            </div>
        </main>
    </div>
</template>

<script>
import { getAgentRateType, getSettlementPrice, changeSettmentPrices } from "../../../http/api";

const pattern = /^0\.\d{0,3}$/;
const costs = ['creditCost', 'debitCost', 'scanCost', 'passCost', 'creditDiscountCost', 'creditSecTransCost']
const rules = {
    debitCapValue: [{ required: true, message: '必填' }]
}
costs.forEach(c => rules[c] = [{ pattern, message: '大于0、小于1且不得超过3位小数' }])

export default {
    name: "Rules",
    data() {
        return {
            settlePricesPicker: false,
            settlePrices: [],
            settlePrice: '',
            rateType: 0,
            readonly: true,
            cost: {
                creditCost: "0.000",
                debitCost: "0.000",
                debitCapValue: "0",
                scanCost: "0.000",
                passCost: "0.000",
                creditDiscountCost: '0.000',
                creditSecTransCost: '0.000'
            },
            secTransOpenConf: false,//秒到交易费率成本展示开关 true 打开 | false 关闭
            creditDiscountOpenConf: false //信用卡特惠费率成本展示开关 true 打开 | false 关闭
        };
    },
    onLoad() {
        this.readonly = this.$store.state.userInfo.agentLevel != 1 || this.$Route.query.agentCode != undefined
        getSettlementPrice(this.$Route.query.agentCode || this.$store.state.userInfo.agentCode, 0).then((res) => {
            if (res.code == "00") {
                const { secTransOpenConf, creditDiscountOpenConf } = res.data
                this.secTransOpenConf = secTransOpenConf
                this.creditDiscountOpenConf = creditDiscountOpenConf
            }
        });
    },
    onReady() {
        this.$refs.uForm.setRules(rules);
    },
    methods: {
        addSettlePrice() {
            this.$Router.push({ name: 'AddSettlePrice', params: { switchs: JSON.stringify({ secTransOpenConf: this.secTransOpenConf, creditDiscountOpenConf: this.creditDiscountOpenConf }) } });
        },
        showSettlePrices() {
            getAgentRateType().then((res) => {
                if (res.code == "00") {
                    this.settlePrices = [];
                    res.data.forEach(i => {
                        this.settlePrices.push({ text: i.modelName, rateType: i.rateType });
                    })
                    this.settlePricesPicker = true;
                }
            });
        },
        settlePriceConfirm(val) {
            this.settlePrice = val[0].label;
            this.rateType = val[0].value
            getSettlementPrice(this.$Route.query.agentCode ? this.$Route.query.agentCode : this.$store.state.userInfo.agentCode, this.rateType).then(res => {
                if (res.code == '00') {
                    const { secTransOpenConf, creditDiscountOpenConf } = res.data
                    this.secTransOpenConf = secTransOpenConf
                    this.creditDiscountOpenConf = creditDiscountOpenConf

                    this.cost = {
                        creditCost: res.data.creditCost,
                        debitCost: res.data.debitCost,
                        debitCapValue: res.data.debitCapValue,
                        scanCost: res.data.scanCost,
                        passCost: res.data.passCost,
                        creditDiscountCost: res.data.creditDiscountCost,
                        creditSecTransCost: res.data.creditSecTransCost
                    };
                    this.settlePricesPicker = false;
                }
            })
        },
        submit() {
            if (this.settlePrice != '') {
                this.$refs.uForm.validate(valid => {
                    if (valid) {
                        var cost = {
                            modelName: this.settlePrice,
                            rateType: this.rateType,
                            creditCost: this.cost.creditCost,
                            debitCost: this.cost.debitCost,
                            debitCapValue: this.cost.debitCapValue,
                            scanCost: this.cost.scanCost,
                            passCost: this.cost.passCost,
                            creditDiscountCost: this.cost.creditDiscountCost,
                            creditSecTransCost: this.cost.creditSecTransCost
                        };
                        changeSettmentPrices(cost).then((res) => {
                            if (res.code == "00") {
                                uni.showToast({
                                    title: res.message,
                                    icon: 'none'
                                });
                            }
                        });
                    }
                })
            } else {
                uni.showToast({
                    title: '模板名称不能为空',
                    icon: 'none'
                });
            }
        }
    }
};
</script>

<style lang="less" scoped>
#rules {
    height: 100%;
    main {
        min-height: 100%;
        padding: 30rpx;
        background-color: #fff;
        > div {
            border-radius: 20rpx;
            box-shadow: #dadada 0 0 10rpx;
            background-color: #f3f5f7;
            padding: 30rpx;
            .header {
                display: flex;
                justify-content: space-between;
                > p {
                    padding-bottom: 26rpx;
                    margin: 0;
                    .span {
                        display: inline-block;
                        width: 8rpx;
                        height: 30rpx;
                        background-color: #004ea9;
                        margin-right: 20rpx;
                        position: relative;
                        top: 6rpx;
                    }
                    span {
                        font-weight: 400;
                        color: #222222;
                    }
                }
            }

            /deep/ .u-field {
                padding-left: 0;
            }
            /deep/ .u-form {
                .u-form-item--left {
                    flex: 1 !important;
                }
                .u-form-item--right {
                    flex: none;
                    width: 140rpx;
                    border-radius: 6rpx;
                    background: #eaeef1;
                    input {
                        text-align: center;
                        color: #4e77d9;
                    }
                }
                .u-form-item__message {
                    text-align: right;
                }
                .custom-button {
                    margin-top: 40rpx;
                }
            }
        }
    }
}
</style>