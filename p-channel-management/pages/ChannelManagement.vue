<template>
    <view id="channelManagement">
        <option-tab :opts="`我的服务商(${number.dirBusCount}),合作服务商(${number.noDirBusCount})`" :current="currentTab" @select="select" />

        <main>
            <section>
                <no-content v-if="show" />
                <view v-show="!show" class="list-data">
                    <section v-for="(a, index) in agents" :key="index">
                        <image :src="head" alt />
                        <div class="right">
                            <div>
                                <p>{{ a.realName != null ? a.realName : a.mobile }}</p>
                                <p>{{ a.agentCode }}</p>
                            </div>
                            <p>
                                {{ a.createTime ? a.createTime.substring(0, 10) : "未知" }}
                            </p>
                        </div>
                    </section>
                </view>

                <u-loadmore v-if="!show" :status="status" @loadmore="loadmore" />
            </section>
        </main>

        <u-back-top :scrollTop="scrollTop" />
    </view>
</template>

<script>
import { queryAgentList } from "../../http/api"
import OptionTab from "../../components/OptionTab.vue"

export default {
    name: "ChannelManagement",
    components: {
        OptionTab,
    },
    data() {
        return {
            head: null,
            avatr: require("../static/images/avatr.png"),
            noAvatr: require("../../static/images/home/<USER>"),
            show: false,
            agents: [],
            total: null,
            currentTab: 0,
            number: {
                dirBusCount: 0,
                noDirBusCount: 0
            },
            scrollTop: 0,
            status: 'loading'
        };
    },
    onLoad() {
        this.getAgent(this.currentTab);
    },
    onPageScroll(e) {
        this.scrollTop = e.scrollTop;
    },
    onReachBottom() {
        this.loadmore()
    },
    methods: {
        select(data) {
            if (this.currentTab != data) {
                this.currentTab = data;
                this.getAgent(data);
            }
        },
        getAgent(type) {
            this.status = 'loading'
            queryAgentList({
                type: type,
                pageNo: 1,
                pageSize: 20
            }).then((res) => {
                if (res.code == "00") {
                    this.head = type == 0 ? this.avatr : this.noAvatr;
                    this.total = res.data.pageInfo.total;
                    this.number.dirBusCount = res.data.dirBusCount;
                    this.number.noDirBusCount = res.data.noDirBusCount;

                    if (res.data.pageInfo.list.length != 0) {
                        this.show = false;
                        this.agents = res.data.pageInfo.list;
                        uni.pageScrollTo({ scrollTop: 0 });

                        if (this.agents.length >= this.total) {
                            // 数据全部加载完成
                            this.status = 'nomore';
                        } else {
                            this.status = 'loadmore';
                        }
                    } else {
                        this.show = true;
                    }
                }
            });
        },
        loadmore() {
            if (this.status == 'nomore') return;
            this.status = 'loading';
            queryAgentList({
                type: this.currentTab,
                pageNo: this.agents.length / 20 + 1,
                pageSize: 20,
            }).then((res) => {
                if (res.code == "00") {
                    this.total = res.data.pageInfo.total;
                    res.data.pageInfo.list.forEach((i) => {
                        this.agents.push(i);
                    });
                    this.loading = false;
                    if (this.agents.length >= this.total) {
                        // 数据全部加载完成
                        this.status = 'nomore';
                    } else {
                        this.status = 'loadmore';
                    }
                }
            });
        },
        agentEquipment(agentCode) {
            this.$Router.push({ name: 'TerminalInventory', params: { agentCode, direct: this.currentTab } });
        },
    },
};
</script>

<style lang="less" scoped>
#channelManagement {
    > main {
        width: 100%;
        > section {
            width: 100vw;
            .list-data {
                > section {
                    display: flex;
                    align-items: center;
                    background-color: #fff;

                    > image {
                        width: 68rpx;
                        height: 68rpx;
                        margin-left: 30rpx;
                    }
                    > .right {
                        flex: 1;
                        display: flex;
                        justify-content: space-between;
                        align-items: flex-start;
                        border-bottom: 2rpx solid #f3f5f7;
                        padding: 30rpx 0;
                        margin-left: 20rpx;
                        div {
                            p {
                                &:last-child {
                                    font-size: 24rpx;
                                    color: #666666;
                                }
                            }
                        }
                        > p {
                            flex-shrink: 0;
                            padding: 6rpx 12rpx;
                            margin-right: 30rpx;
                            background: #f3f5f7;
                            border-radius: 8rpx;
                            font-size: 24rpx;
                            color: #999999;
                        }
                    }
                    p {
                        margin: 0;
                        padding: 0;
                    }
                }
            }
        }
    }
}
</style>