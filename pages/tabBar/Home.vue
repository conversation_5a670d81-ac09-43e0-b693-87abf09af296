<template>
  <view id="home">
    <u-navbar :is-back="false" :background="navbarBg" :border-bottom="showNavBarBorder">
      <view class="navbar-content">
        <image :src="appInfo.appLogoUrl || defaultLogoUrl" mode="widthFix" />
        <text>{{ appInfo.appName }}</text>
      </view>
    </u-navbar>

    <main>
      <home-channel v-if="currentMode == 1" ref="channel" />
      <home-direct v-else ref="direct" />
    </main>

    <!-- 隐私协议 -->
    <!-- #ifdef MP-WEIXIN -->
    <privacy-popup ref="privacyComponent"></privacy-popup>
    <!-- #endif -->
  </view>
</template>

<script>
import HomeChannel from '../../components/home/<USER>';
import HomeDirect from '../../components/home/<USER>';
import PrivacyPopup from '../../components/privacy-popup/privacy-popup.vue';

// 获取系统状态栏的高度
let systemInfo = uni.getSystemInfoSync();
export default {
  name: 'Home',
  components: { HomeChannel, HomeDirect, PrivacyPopup },
  provide() {
    return {
      pageScrollTop: this.pageScrollTop
    };
  },
  data() {
    return {
      navbarBg: {
        backgroundColor: 'rgba(255,255,255,0)',
        color: '#fff'
      },
      defaultLogoUrl: require('../../static/images/common/logo.jpg'),
      showNavBarBorder: false,
      pageScrollTop: { scrollTop: 0 }
    };
  },
  computed: {
    currentMode() {
      return this.$store.state.currentMode;
    },
    navbarHeight() {
      let height = systemInfo.platform == 'ios' ? 44 : 48;
      return height + systemInfo.statusBarHeight;
    },
    appInfo() {
      return this.$store.state.appInfo;
    }
  },
  watch: {
    currentMode() {
      this.$nextTick(() => {
        this.$refs[this.currentMode == 1 ? 'channel' : 'direct'].refreshData();
      });
    }
  },
  onLoad() {},
  onShow() {
    this.$nextTick(() => {
      this.$refs[this.currentMode == 1 ? 'channel' : 'direct'].refreshData();
    });
  },
  onPageScroll(e) {
    if (e.scrollTop < this.navbarHeight) {
      this.navbarBg.backgroundColor = `rgba(255,255,255,${e.scrollTop / this.navbarHeight})`;
      this.navbarBg.color = '#fff';
      this.showNavBarBorder = false;
    } else {
      this.navbarBg.backgroundColor = 'rgba(255,255,255,1)';
      this.navbarBg.color = '#303133';
      this.showNavBarBorder = true;
    }
    this.pageScrollTop.scrollTop = e.scrollTop;
  },
  methods: {}
};
</script>

<style lang="scss" scoped>
#home {
  position: relative;
  height: 100%;
  .navbar-content {
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    width: 100%;
    image {
      width: 62rpx;
      height: 62rpx;
      border-radius: 10rpx;
    }
    text {
      width: 400rpx;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      margin-left: 12rpx;
    }
  }
  main {
    margin-top: 20rpx;
  }
}
</style>
