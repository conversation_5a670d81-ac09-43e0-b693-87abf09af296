<template>
    <view id="increment" class="modern-increment-page">
        <!-- 现代化导航栏 -->
        <view class="increment-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
            <view class="navbar-content">
                <text class="page-title">增值业务</text>
                <view class="help-button" @click="showPlayPresent = true">
                    <u-icon size="28" name="question-circle" color="#FFFFFF" />
                    <text class="help-text">玩法介绍</text>
                </view>
            </view>
        </view>

        <!-- 业务卡片网格 -->
        <view class="business-container" :style="{ paddingTop: navbarHeight + 'px' }">
            <view class="business-grid">
                <view
                    v-for="(b, index) in business"
                    :key="index"
                    class="business-card"
                    @click="toLink(b.navName)"
                >
                    <view class="card-background" :style="{ background: getCardGradient(b.bgColor) }">
                        <view class="card-decoration">
                            <view class="decoration-circle"></view>
                            <view class="decoration-wave"></view>
                        </view>
                    </view>
                    <view class="card-content">
                        <view class="business-icon">
                            <u-icon :name="getBusinessIcon(index)" size="48" color="#FFFFFF" />
                        </view>
                        <text class="business-name">{{ b.name }}</text>
                        <view class="card-arrow">
                            <u-icon name="arrow-right" size="24" color="rgba(255,255,255,0.8)" />
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 玩法介绍弹窗 -->
        <u-modal v-model="showPlayPresent" :showConfirmButton="false" :show-title="false">
            <div class="present">
                <span class="title">玩法介绍</span>
                <div class="content">
                    <p>1.手游返佣: 进入手游列表，分享推广二维码到微信，玩家识别二维码后注册手游账号，在游戏中充值消费；</p>
                    <p>2.电商返佣：进入在线商城，分享商品二维码到微信，客户识别商品二维码后进入小程序下单成功；</p>
                    <p>以上推广场景，均可让推广员赚取返佣分润，佣金比例详情见对应产品列表。</p>
                </div>
                <view class="close" @click="showPlayPresent = false">
                    <u-icon name="close-circle" size="50" />
                </view>
            </div>
        </u-modal>
    </view>
</template>

<script>
import { getListAgentCode } from '../../http/api'
export default {
    name: 'Increment',
    data() {
        return {
            showPlayPresent: false,
            business: [
                {
                    name: '手游推广',
                    navName: 'GameManagement',
                    bgColor: '#17b1aa'
                },
                {
                    name: '在线商城',
                    navName: 'MiniProgramManagement',
                    bgColor: '#e54d42'
                }
            ],
            navbarBg: {
                backgroundColor: '#1A73E8'
            },
            statusBarHeight: 0,
            navbarHeight: 0
        }
    },
    mounted() {
        const systemInfo = uni.getSystemInfoSync()
        this.statusBarHeight = systemInfo.statusBarHeight
        this.navbarHeight = this.statusBarHeight + (systemInfo.platform === 'ios' ? 44 : 48) + 120 // 120rpx for custom content
    },
    onLoad() {
        getListAgentCode().then(res => {
            if (res.code == '00') {
                const agents = res.data || []
                if (agents.includes(this.$store.state.userInfo.superAgentCode)) {
                    this.business.push({
                        name: '申请信用卡',
                        navName: 'ApplyCreditCard',
                        bgColor: '#79a789'
                    })
                }
            }
        })
    },
    methods: {
        toLink(name) {
            this.$Router.push({ name })
        },
        getBusinessIcon(index) {
            const icons = ['game-controller', 'shop', 'gift', 'hotel']
            return icons[index] || 'star'
        },
        getCardGradient(bgColor) {
            const gradients = {
                '#17b1aa': 'linear-gradient(135deg, #17b1aa 0%, #20c997 100%)',
                '#e54d42': 'linear-gradient(135deg, #e54d42 0%, #ff6b6b 100%)',
                '#f37b1d': 'linear-gradient(135deg, #f37b1d 0%, #ffa726 100%)',
                '#79a789': 'linear-gradient(135deg, #79a789 0%, #81c784 100%)'
            }
            return gradients[bgColor] || `linear-gradient(135deg, ${bgColor} 0%, ${bgColor}dd 100%)`
        }
    }
}
</script>

<style lang="scss" scoped>
.modern-increment-page {
    height: 100%;
    background: $bg-secondary;

    .increment-navbar {
        background: $gradient-primary;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 999;

        .navbar-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: $spacing-lg;

            .page-title {
                font-size: $font-size-xl;
                font-weight: $font-weight-bold;
                color: #FFFFFF;
            }

            .help-button {
                display: flex;
                align-items: center;
                background: rgba(255, 255, 255, 0.2);
                padding: $spacing-sm $spacing-md;
                border-radius: $radius-large;
                backdrop-filter: blur(10rpx);
                transition: all 0.3s ease;

                &:active {
                    transform: scale(0.95);
                    background: rgba(255, 255, 255, 0.3);
                }

                .help-text {
                    margin-left: $spacing-xs;
                    font-size: $font-size-sm;
                    color: #FFFFFF;
                }
            }
        }
    }

    .business-container {
        padding: $spacing-lg;

        .business-grid {
            display: flex;
            flex-wrap: wrap;
            gap: $spacing-lg;

            .business-card {
                width: calc(50% - #{$spacing-lg / 2});
                height: 240rpx;
                position: relative;
                border-radius: $radius-large;
                overflow: hidden;
                box-shadow: $shadow-medium;
                transition: all 0.3s ease;

                &:active {
                    transform: translateY(4rpx);
                    box-shadow: $shadow-light;
                }

                .card-background {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;

                    .card-decoration {
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;

                        .decoration-circle {
                            position: absolute;
                            width: 120rpx;
                            height: 120rpx;
                            background: rgba(255, 255, 255, 0.1);
                            border-radius: 50%;
                            top: -60rpx;
                            right: -60rpx;
                        }

                        .decoration-wave {
                            position: absolute;
                            bottom: 0;
                            left: 0;
                            right: 0;
                            height: 40rpx;
                            background: rgba(255, 255, 255, 0.1);
                            border-radius: 40rpx 40rpx 0 0;
                        }
                    }
                }

                .card-content {
                    position: relative;
                    z-index: 1;
                    padding: $spacing-lg;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;

                    .business-icon {
                        width: 80rpx;
                        height: 80rpx;
                        background: rgba(255, 255, 255, 0.2);
                        border-radius: $radius-medium;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        backdrop-filter: blur(10rpx);
                    }

                    .business-name {
                        font-size: $font-size-lg;
                        font-weight: $font-weight-semibold;
                        color: #FFFFFF;
                        margin: $spacing-md 0;
                        flex: 1;
                    }

                    .card-arrow {
                        align-self: flex-end;
                        width: 48rpx;
                        height: 48rpx;
                        background: rgba(255, 255, 255, 0.2);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        backdrop-filter: blur(10rpx);
                    }
                }
            }
        }
    }
}
</style>
