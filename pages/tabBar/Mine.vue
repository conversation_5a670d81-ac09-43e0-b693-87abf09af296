<template>
  <view id="mine">
    <view class="header">
      <view class="content">
        <image v-if="avatar" :src="avatar" mode="aspectFit" @click="changeAvatar" />
        <image v-else src="../../static/images/home/<USER>" mode="widthFix" @click="changeAvatar" />
        <text
          >{{ userInfo.realName }}
          <text style="margin-left: 1em" v-if="currentMode == 2">{{
            userInfo.memberLevel ? userInfo.memberLevel.replace('C', 'V') : ''
          }}</text></text
        >
        <p v-if="userInfo.isMobileVerify">{{ userInfo.loginName }}</p>
        <span class="agent-code" v-else>{{ userInfo.agentCode }}</span>
      </view>
    </view>

    <view class="share-card" :class="{ isMobileVerify: userInfo.isMobileVerify }">
      <template v-if="!userInfo.isMobileVerify">
        您还没有绑定登录手机号,<span @click="$Router.push({ name: 'BindPhone' })">前往绑定 ></span>
      </template>
    </view>

    <view class="menus-card">
      <u-cell-group :border="false">
        <!--  #ifdef  MP-WEIXIN -->
        <u-cell-item icon="rmb-circle" title="收益账户" @click="toMenuItem('WithdrawMoney')" />
        <!--  #endif -->
        <!--  #ifdef  APP-PLUS -->
        <u-cell-item icon="rmb-circle" v-if="isIosCheckPass == 1" title="收益账户" @click="toMenuItem('WithdrawMoney')" />
        <!--  #endif -->
        <u-cell-item icon="account" title="个人信息" @click="toMenuItem('SelfBaseInfo')" />
        <u-cell-item icon="order" title="账单" @click="toMenuItem('Bill')" />
        <u-cell-item icon="coupon" title="结算方式" @click="toMenuItem('MyBankCard')" />
        <u-cell-item v-if="currentMode == 2" icon="integral" title="设备兑换券" @click="toMenuItem('MyVoucher')" />
        <u-cell-item
          v-if="userInfo.payChannelCode === '1003' && userInfo.taxationRegisterStatus == 0"
          icon="file-text"
          title="税筹注册"
          value="去注册"
          @click="toMenuItem('TaxRaiseRegister')"
        />
        <p class="split-line" />
        <u-cell-item icon="setting" title="设置" @click="toMenuItem('Set')" />
        <u-cell-item icon="bell" title="消息中心" @click="toMenuItem('Notifications')">
          <text v-show="ureadCount" slot="right-icon" class="uread-count">有新消息</text>
        </u-cell-item>
        <u-cell-item icon="account-fill" title="关于我们" @click="toMenuItem('AboutUs')" />
        <!--  #ifdef  APP-PLUS -->
        <u-cell-item icon="tags" :border-bottom="false" title="检查更新" :value="`v ${version}`" @click="checkUpdate" />
        <!--  #endif -->
      </u-cell-group>
    </view>
  </view>
</template>

<script>
import { getAllNotifications, appVersion, getHeadPortrait, headPortraitSetting, loginSwitch } from '../../http/api';
import { mapState } from 'vuex';

export default {
  name: 'Mine',
  data() {
    return {
      oldTime: null,
      version: typeof plus === 'object' && plus.runtime.version,
      avatar: ''
    };
  },
  computed: {
    ...mapState(['userInfo', 'currentMode', 'isIosCheckPass']),
    ureadCount() {
      return this.$store.state.appNotifications.filter(a => a.readingSatus == 0).length || 0;
    }
  },
  onLoad() {
    this.getAvatar();
  },
  onShow() {
    // if (this.oldTime == null) {
    //   this.oldTime = Date.now()
    //   this.getNotifications()
    // } else {
    //   if (Date.now() > this.oldTime + 2 * 60 * 1000) {
    //     this.oldTime = Date.now()
    //     this.getNotifications()
    //   }
    // }
  },
  methods: {
    getAvatar() {
      getHeadPortrait().then(res => {
        if (res.code == '00') {
          this.avatar = res.data.headPortraitImagePath || '';
        }
      });
    },
    changeAvatar() {
    //#ifdef APP-PLUS
      const platform = uni.getSystemInfoSync().platform;
      if(platform === 'ios' && this.isIosCheckPass != 1) return
    //#endif

      uni.chooseImage({
        count: 1,
        success: async res => {
          const base64Data = await this.pathToBase64(res.tempFilePaths[0]);
          if (base64Data) {
            console.log(base64Data);
            const base64DataSplice = base64Data.split(',')[1];
            function guessImageTypeFromBase64(str) {
              switch (str.charAt(0)) {
                case '/':
                  return 'jpeg';
                case 'i':
                  return 'png';
                case 'R':
                  return 'gif';
                case 'U':
                  return 'webp';
                case 'Q':
                  return 'bmp';
                default:
                  return 'png';
              }
            }
            headPortraitSetting({ img: base64DataSplice, imgSuffix: guessImageTypeFromBase64(base64DataSplice) }).then(res2 => {
              if (res2.code == '00') {
                this.getAvatar();
              }
            });
          }
        }
      });
    },
    toMenuItem(name) {
      this.$Router.push({ name });
    },
    getNotifications() {
      getAllNotifications().then(res => {
        if (res.code == '00') {
          var { officialNOtice = [], systemNotice = [] } = res.data;
          officialNOtice ? officialNOtice.forEach(o => (o.type = 0)) : (officialNOtice = []);
          systemNotice ? systemNotice.forEach(s => (s.type = 1)) : (systemNotice = []);
          var appNotificationsMap = [...officialNOtice, ...systemNotice];
          const appNotifications = [];
          appNotificationsMap.forEach(i => {
            appNotifications.push({
              id: i.id,
              type: i.type,
              noticeTitle: i.noticeTitle,
              noticeContent: i.noticeContent,
              createTime: i.createTime,
              readingSatus: 0 // 未读
            });
          });
          const storageNotifications = uni.getStorageSync('appNotifications') || [];
          if (storageNotifications.length) {
            appNotifications.forEach(i => {
              storageNotifications.forEach(j => {
                if (i.id == j.id && i.type == j.type) {
                  i.readingSatus = j.readingSatus;
                }
              });
            });
          }
          this.$store.commit('SET_APPNOTIFICATIONS', appNotifications);
        }
      });
    },
    checkUpdate() {
      const platform = uni.getSystemInfoSync().platform;
      // 获取本地应用资源版本号
      plus.runtime.getProperty(plus.runtime.appid, inf => {
        //获取服务器的版本号
        appVersion({ abbr: 'shtop', version: inf.version, fileType: platform === 'android' ? 10 : 20 }).then(({ data }) => {
          if (!data) return;
          const { downloadURL, support, newest, buildUpdateDescription } = data;

          const convertData = {
            describe: buildUpdateDescription, // 版本更新内容 支持<br>自动换行
            edition_url: platform === 'android' ? downloadURL : 'itms-apps://itunes.apple.com/cn/app/1617036314', //apk下载地址 或者 跳转appstore
            edition_force: Number(!support), //是否强制更新 0代表否 1代表是
            package_type: 0, //0是整包升级（apk或者appstore或者安卓应用市场） 1是wgt升级
            edition_issue: 1 //是否发行  0否 1是 为了控制上架应用市场审核时不能弹出热更新框
          };
          //判断后台返回是否最新版
          if (newest === 0) {
            //跳转更新页面 （注意！！！如果pages.json第一页的代码里有一打开就跳转其他页面的操作，下面这行代码最好写在setTimeout里面设置延时3到5秒再执行）
            uni.navigateTo({
              url: '/uni_modules/rt-uni-update/components/rt-uni-update/rt-uni-update?obj=' +  encodeURIComponent(JSON.stringify(convertData))
            });
          }

          if (newest === 1) return this.$u.toast('当前已是最新版本');
        });
      });
    },
    pathToBase64(path) {
      return new Promise((resolve, reject) => {
        if (typeof plus === 'object') {
          plus.io.resolveLocalFileSystemURL(
            this.getLocalFilePath(path),
            function (entry) {
              entry.file(
                function (file) {
                  var fileReader = new plus.io.FileReader();
                  fileReader.onload = function (data) {
                    resolve(data.target.result);
                  };
                  fileReader.onerror = function (error) {
                    reject(error);
                  };
                  fileReader.readAsDataURL(file);
                },
                function (error) {
                  reject(error);
                }
              );
            },
            function (error) {
              reject(error);
            }
          );
          return;
        }
        if (typeof wx === 'object' && wx.canIUse('getFileSystemManager')) {
          wx.getFileSystemManager().readFile({
            filePath: path,
            encoding: 'base64',
            success: function (res) {
              resolve('data:image/png;base64,' + res.data);
            },
            fail: function (error) {
              reject(error);
            }
          });
          return;
        }
        reject(new Error('not support'));
      });
    },
    getLocalFilePath(path) {
      if (
        path.indexOf('_www') === 0 ||
        path.indexOf('_doc') === 0 ||
        path.indexOf('_documents') === 0 ||
        path.indexOf('_downloads') === 0
      ) {
        return path;
      }
      if (path.indexOf('file://') === 0) {
        return path;
      }
      if (path.indexOf('/storage/emulated/0/') === 0) {
        return path;
      }
      if (path.indexOf('/') === 0) {
        var localFilePath = plus.io.convertAbsoluteFileSystem(path);
        if (localFilePath !== path) {
          return localFilePath;
        } else {
          path = path.substr(1);
        }
      }
      return '_www/' + path;
    }
  }
};
</script>

<style lang="scss" scoped>
#mine {
  min-height: 100%;
  .header {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 424rpx;
    background-image: url('../../static/images/home/<USER>');
    background-size: 100%;
    background-repeat: no-repeat;
    .content {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 50rpx;
      color: #fff;
      > image {
        width: 100rpx;
        height: 100rpx;
        border-radius: 50%;
      }
      > text {
        margin-top: 20rpx;
        font-size: 32rpx;
      }
      .agent-code {
        margin-top: 16rpx;
        font-size: 28rpx;
      }
      > p {
        margin: 16rpx 0 0;
        display: flex;
        align-items: center;
        font-size: 28rpx;
        > span {
          margin-left: 20rpx;
          color: #004ea9;
          // font-weight: bold;
        }
      }
    }
  }
  .share-card {
    margin-top: -40rpx;
    width: 100%;
    height: 80rpx;
    background-color: rgba(#fff, 0.8);
    letter-spacing: 0.06em;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #004ea9;
    > span {
      margin-left: 20rpx;
      font-weight: bolder;
      animation: headShake 3s infinite;
    }
  }
  .isMobileVerify {
    background-color: rgba(#fff, 0.3);
  }
  .menus-card {
    margin-bottom: 20rpx;
    background-color: #fff;
    border-radius: 8rpx;
    /deep/ .u-cell {
      padding: 20rpx 30rpx;
    }
    /deep/ .u-cell-item-box {
      background-color: transparent;
    }
    .uread-count {
      position: relative;
      color: #888;
      &::after {
        content: '';
        position: absolute;
        top: -4rpx;
        right: -10rpx;
        width: 10rpx;
        height: 10rpx;
        border-radius: 50%;
        background-color: red;
      }
    }
    .split-line {
      height: 16rpx;
      background-color: #f3f5f7;
    }
  }
}
@keyframes headShake {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }

  6.5% {
    -webkit-transform: translateX(-6px) rotateY(-9deg);
    transform: translateX(-6px) rotateY(-9deg);
  }

  18.5% {
    -webkit-transform: translateX(5px) rotateY(7deg);
    transform: translateX(5px) rotateY(7deg);
  }

  31.5% {
    -webkit-transform: translateX(-3px) rotateY(-5deg);
    transform: translateX(-3px) rotateY(-5deg);
  }

  43.5% {
    -webkit-transform: translateX(2px) rotateY(3deg);
    transform: translateX(2px) rotateY(3deg);
  }

  50% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
</style>
