<template>
    <view id="data-page">
        <u-navbar :is-back="false" :background="navbarBg" :border-bottom="false">
            <view class="navbar-content">
                <!-- #ifdef MP-WEIXIN -->
                <u-tabs-swiper ref="uTabs" :list="tabsType" :bar-width="60" bg-color="transparent" active-color="#ffffff" inactive-color="#e5e5e5" :current="navBarCurrent" @change="changeNavBarType" />
                <!-- #endif -->
                <!-- #ifdef APP-PLUS -->
                <u-tabs-swiper ref="uTabs" :is-scroll="false" :list="tabsType" :bar-width="60" bg-color="transparent" active-color="#ffffff" inactive-color="#e5e5e5" :current="navBarCurrent" @change="changeNavBarType" />
                <!-- #endif -->
            </view>
        </u-navbar>

        <main :style="'height:calc(100%' + ' - ' + navbarHeight + ')'">
            <swiper :current="swiperCurrent" @transition="transition" @animationfinish="animationfinish">
                <swiper-item>
                    <scroll-view scroll-y refresher-enabled :refresher-threshold="160" :refresher-triggered="triggered" refresher-background="white" @refresherrefresh="onRefresh">
                        <data-chart ref="chart" />
                    </scroll-view>
                </swiper-item>
                <swiper-item>
                    <scroll-view scroll-y refresher-enabled :refresher-threshold="160" :refresher-triggered="triggered" refresher-background="white" @refresherrefresh="onRefresh">
                        <data-ranking ref="ranking" />
                    </scroll-view>
                </swiper-item>
                <swiper-item>
                    <scroll-view scroll-y refresher-enabled :refresher-threshold="160" :refresher-triggered="triggered" refresher-background="white" @refresherrefresh="onRefresh">
                        <data-chart2 ref="chart2" />
                    </scroll-view>
                </swiper-item>
                <swiper-item>
                    <scroll-view scroll-y refresher-enabled :refresher-threshold="160" :refresher-triggered="triggered" refresher-background="white" @refresherrefresh="onRefresh">
                        <data-ranking2 ref="ranking2" />
                    </scroll-view>
                </swiper-item>
            </swiper>
        </main>
    </view>
</template>

<script>
import DataChart from '../../components/data/DataChart.vue'
import DataChart2 from '../../components/data/DataChart2.vue'
import DataRanking from '../../components/data/DataRanking.vue'
import DataRanking2 from '../../components/data/DataRanking2.vue'
let systemInfo = uni.getSystemInfoSync()

export default {
    name: 'DataPage',
    components: { DataChart, DataChart2, DataRanking, DataRanking2 },
    data() {
        return {
            navBarCurrent: 0,
            swiperCurrent: 0,
            tabsType: [{ name: '数据分析' }, { name: '数据排名' }, { name: '单通道趋势' }, { name: '单通道排名' }],
            navbarBg: {
                backgroundColor: 'rgb(5,56,155)'
            },
            triggered: false
        }
    },
    computed: {
        navbarHeight() {
            let height = systemInfo.platform == 'ios' ? 44 : 48
            return height + systemInfo.statusBarHeight + 'px'
        }
    },
    methods: {
        onRefresh() {
            if (this._freshing) return
            this._freshing = true
            this.triggered = true
            const componentsArr = ['chart', 'ranking', 'chart2', 'ranking2']
            const componentRef = componentsArr[this.navBarCurrent]
            this.$refs[componentRef].refreshData()
            setTimeout(() => {
                this.triggered = false
                this._freshing = false
            }, 500)
        },
        transition(e) {
            let dx = e.detail.dx
            this.$refs.uTabs.setDx(dx)
        },
        animationfinish(e) {
            let current = e.detail.current
            this.$refs.uTabs.setFinishCurrent(current)
            this.swiperCurrent = current
            this.navBarCurrent = current
        },
        changeNavBarType(index) {
            this.swiperCurrent = index
        }
    }
}
</script>

<style lang="scss" scoped>
#data-page {
    height: 100%;
    ::v-deep .u-navbar-inner {
        .u-slot-content {
            width: 100%;
            .navbar-content {
                width: 100%;
            }
        }
    }
    > main {
        swiper {
            height: 100%;
            scroll-view {
                width: 100%;
                height: 100%;
            }
        }
    }
}
</style>
