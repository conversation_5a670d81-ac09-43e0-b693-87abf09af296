<template>
    <view id="data-page" class="modern-data-page">
        <!-- 现代化导航栏 -->
        <view class="data-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
            <view class="navbar-content">
                <text class="page-title">数据中心</text>
                <view class="navbar-tabs">
                    <view
                        v-for="(tab, index) in tabsType"
                        :key="index"
                        class="tab-item"
                        :class="{ active: navBarCurrent === index }"
                        @click="changeNavBarType(index)"
                    >
                        <text class="tab-text">{{ tab.name }}</text>
                        <view v-if="navBarCurrent === index" class="tab-indicator"></view>
                    </view>
                </view>
            </view>
        </view>

        <main :style="'height:calc(100%' + ' - ' + navbarHeight + ')'">
            <swiper :current="swiperCurrent" @transition="transition" @animationfinish="animationfinish">
                <swiper-item>
                    <scroll-view scroll-y refresher-enabled :refresher-threshold="160" :refresher-triggered="triggered" refresher-background="white" @refresherrefresh="onRefresh">
                        <data-chart ref="chart" />
                    </scroll-view>
                </swiper-item>
                <swiper-item>
                    <scroll-view scroll-y refresher-enabled :refresher-threshold="160" :refresher-triggered="triggered" refresher-background="white" @refresherrefresh="onRefresh">
                        <data-ranking ref="ranking" />
                    </scroll-view>
                </swiper-item>
                <swiper-item>
                    <scroll-view scroll-y refresher-enabled :refresher-threshold="160" :refresher-triggered="triggered" refresher-background="white" @refresherrefresh="onRefresh">
                        <data-chart2 ref="chart2" />
                    </scroll-view>
                </swiper-item>
                <swiper-item>
                    <scroll-view scroll-y refresher-enabled :refresher-threshold="160" :refresher-triggered="triggered" refresher-background="white" @refresherrefresh="onRefresh">
                        <data-ranking2 ref="ranking2" />
                    </scroll-view>
                </swiper-item>
            </swiper>
        </main>
    </view>
</template>

<script>
import DataChart from '../../components/data/DataChart.vue'
import DataChart2 from '../../components/data/DataChart2.vue'
import DataRanking from '../../components/data/DataRanking.vue'
import DataRanking2 from '../../components/data/DataRanking2.vue'
let systemInfo = uni.getSystemInfoSync()

export default {
    name: 'DataPage',
    components: { DataChart, DataChart2, DataRanking, DataRanking2 },
    data() {
        return {
            navBarCurrent: 0,
            swiperCurrent: 0,
            tabsType: [{ name: '数据分析' }, { name: '数据排名' }, { name: '单通道趋势' }, { name: '单通道排名' }],
            navbarBg: {
                backgroundColor: '#1A73E8'
            },
            triggered: false,
            statusBarHeight: 0
        }
    },
    mounted() {
        const systemInfo = uni.getSystemInfoSync()
        this.statusBarHeight = systemInfo.statusBarHeight
    },
    computed: {
        navbarHeight() {
            let height = systemInfo.platform == 'ios' ? 44 : 48
            return height + systemInfo.statusBarHeight + 'px'
        }
    },
    methods: {
        onRefresh() {
            if (this._freshing) return
            this._freshing = true
            this.triggered = true
            const componentsArr = ['chart', 'ranking', 'chart2', 'ranking2']
            const componentRef = componentsArr[this.navBarCurrent]
            this.$refs[componentRef].refreshData()
            setTimeout(() => {
                this.triggered = false
                this._freshing = false
            }, 500)
        },
        transition(e) {
            let dx = e.detail.dx
            this.$refs.uTabs.setDx(dx)
        },
        animationfinish(e) {
            let current = e.detail.current
            this.$refs.uTabs.setFinishCurrent(current)
            this.swiperCurrent = current
            this.navBarCurrent = current
        },
        changeNavBarType(index) {
            this.swiperCurrent = index
        }
    }
}
</script>

<style lang="scss" scoped>
.modern-data-page {
    height: 100%;
    background: $bg-secondary;

    .data-navbar {
        background: $gradient-primary;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 999;

        .navbar-content {
            padding: $spacing-lg;

            .page-title {
                font-size: $font-size-xl;
                font-weight: $font-weight-bold;
                color: #FFFFFF;
                text-align: center;
                margin-bottom: $spacing-lg;
            }

            .navbar-tabs {
                display: flex;
                background: rgba(255, 255, 255, 0.1);
                border-radius: $radius-large;
                padding: $spacing-xs;

                .tab-item {
                    flex: 1;
                    position: relative;
                    padding: $spacing-sm $spacing-md;
                    border-radius: $radius-medium;
                    text-align: center;
                    transition: all 0.3s ease;

                    &.active {
                        background: rgba(255, 255, 255, 0.2);

                        .tab-text {
                            color: #FFFFFF;
                            font-weight: $font-weight-semibold;
                        }
                    }

                    .tab-text {
                        font-size: $font-size-sm;
                        color: rgba(255, 255, 255, 0.7);
                        transition: all 0.3s ease;
                    }

                    .tab-indicator {
                        position: absolute;
                        bottom: -$spacing-xs;
                        left: 50%;
                        transform: translateX(-50%);
                        width: 24rpx;
                        height: 6rpx;
                        background: #FFFFFF;
                        border-radius: 3rpx;
                    }
                }
            }
        }
    }

    > main {
        padding-top: 200rpx; // 为固定导航栏留出空间
        height: 100%;

        swiper {
            height: 100%;

            scroll-view {
                width: 100%;
                height: 100%;
                background: $bg-secondary;
            }
        }
    }
}
</style>
