<template>
  <div id="addCashBackModel">
    <main>
      <u-form :model="addModel" ref="uForm" class="u-form" label-width="auto">
        <u-form-item label="政策名称" prop="modelName">
          <u-input v-model="addModel.modelName" placeholder="请输入政策名称" />
        </u-form-item>
        <u-form-item label="返现类型">
          <u-input
            type="select"
            :select-open="showBusinessPicker"
            placeholder="请选择返现类型"
            :value="businessTypeVal"
            @click="showBusinessPicker = true"
            :clearable="false"
          />
        </u-form-item>
        <u-form-item label="支付通道" prop="payOrgCode" v-if="addModel.businessModelType === 6">
          <u-input
            type="select"
            :select-open="payOrgCodesPicker"
            placeholder="请选择支付通道"
            :value="payOrgCode"
            @click="showPayOrgCodes"
          />
        </u-form-item>
        <u-form-item label="计费方式">
          <u-radio-group v-model="addModel.feeType">
            <u-radio :name="1">固定金额</u-radio>
            <u-radio :name="2">百分比</u-radio>
          </u-radio-group>
        </u-form-item>
        <u-form-item v-if="addModel.feeType === 1" label="返现金额" prop="cashbackAmount">
          <u-input type="digit" v-model="addModel.cashbackAmount" placeholder="请输入返现金额(元)" />
        </u-form-item>
        <u-form-item v-else label="返现比例">
          <u-input type="digit" v-model="addModel.cashbackAmount" placeholder="单位%, 范围0-100" />
        </u-form-item>

        <u-select v-model="showBusinessPicker" mode="single-column" :list="businessType" @confirm="pickerConfirm" />
        <u-select v-model="payOrgCodesPicker" :list="payOrgCodes" label-name="name" value-name="code" @confirm="payOrgCodeConfirm" />
      </u-form>

      <view class="submit-btn">
        <u-button type="primary" @click="submit">提 交</u-button>
      </view>
    </main>
  </div>
</template>

<script>
import { addRule, getChannel } from '../../../../http/api';

const pattern = /^([1-9]\d{0,}|0|0\.\d{0,1}[1-9]|[1-9]\d{0,}\.\d{0,1}[1-9])$/;
const pattern2 = /^(\d{1,2}(\.\d{1,4})?|100)$/;

const rules = {
  cashbackAmount: [
    { required: true, message: '必填' },
    { pattern, message: '必须符合金额规则且不得超过2位小数' }
  ],
  modelName: [{ required: true, message: '必填' }]
};
export default {
  name: 'AddCashBackModel',
  data() {
    return {
      addModel: {
        feeType: 1, // 计费方式 1-固定金额，2-百分比(显示 单位% 范围0-100)
        cashbackAmount: '', //返现金额,必填
        modelName: '', //模板名称,必填
        businessModelType: 2, // 返现类型 1-优惠费率 ,必填
        payOrgCode: ''
      },
      showBusinessPicker: false,
      businessType: [
        // {
        //   label: '商户优惠费率',
        //   value: 1
        // },
        {
          label: '出款服务费',
          value: 2
        },
        {
          label: '管理服务费',
          value: 3
        },
        {
          label: '网申信用卡核卡',
          value: 4
        },
        {
          label: '网申信用卡激活',
          value: 5
        },
        {
          label: '商户费率调价分成',
          value: 6
        }
      ],
      businessTypeVal: '出款服务费',
      payOrgCodesPicker: false,
      payOrgCodes: [],
      payOrgCode: ''
    };
  },
  onLoad() {},
  onReady() {
    this.$refs.uForm.setRules(rules);
  },
  methods: {
    showPayOrgCodes() {
      this.payOrgCodes = [];
      getChannel().then(res => {
        if (res.code == '00') {
          if (res.data.length) {
            this.payOrgCodes = res.data;
            this.payOrgCodesPicker = true;
          } else {
            this.$u.toast('暂无可选支付通道！');
          }
        }
      });
    },
    payOrgCodeConfirm([{ label, value }]) {
      this.payOrgCode = label;
      this.addModel.payOrgCode = value;
      this.payOrgCodesPicker = false;
    },
    pickerConfirm(val) {
      this.businessTypeVal = val[0].label;
      this.addModel.businessModelType = val[0].value;
      this.showBusinessPicker = false;
    },
    submit() {
      if (this.addModel.businessModelType === 6 && !this.addModel.payOrgCode) {
        return this.$u.toast('请选择支付通道！');
      }
      this.$refs.uForm.validate(valid => {
        if (valid) {
          if (this.addModel.feeType === 2 && (!this.addModel.cashbackAmount || !pattern2.test(this.addModel.cashbackAmount))) {
            return uni.showToast({
              title: '返现比例必填且允许范围0-100内最多4位小数的数字！',
              icon: 'none'
            });
          }
          addRule(this.addModel).then(res => {
            if (res.code == '00') {
              uni.showToast({
                title: '添加成功！',
                icon: 'none'
              });
              setTimeout(() => {
                this.$Router.back(1);
              }, 1000);
            }
          });
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
#addCashBackModel {
  height: 100%;
  padding-top: 20rpx;
  main {
    height: 100%;
    padding: 0 30rpx;
    background: white;
  }
  .submit-btn {
    margin: 150rpx 30rpx 30rpx;
  }
}
</style>
