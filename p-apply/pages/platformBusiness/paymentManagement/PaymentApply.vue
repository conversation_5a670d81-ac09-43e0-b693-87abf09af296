<template>
    <div id="paymentApply">
        <u-form :model="applyInfo" ref="uForm" class="u-form" label-width="auto">
            <section>
                <u-form-item label="终端数量(个)" prop="terminalNum">
                    <u-input type="number" v-model="applyInfo.terminalNum" placeholder="请填写终端数量" />
                </u-form-item>
                <u-form-item label="已付金额(元)" prop="firstAmount">
                    <u-input type="number" v-model="applyInfo.firstAmount" placeholder="请填写已付金额" />
                </u-form-item>
                <u-form-item label="总金额(元)" prop="allAmount">
                    <u-input type="number" v-model="applyInfo.allAmount" placeholder="请填写总金额" />
                </u-form-item>
                <u-form-item label="备注:">
                    <u-input v-model="applyInfo.remark" placeholder="请填写备注" />
                </u-form-item>
            </section>

            <footer class="custom-button">
                <button @click="submit">提交</button>
            </footer>
        </u-form>
    </div>
</template>

<script>
import {
    applyLoansOrder
} from "../../../../http/api";

const pattern = /^\d+(?=\.{0,1}\d+$|$)/;
const pattern2 = /^\+?[1-9][0-9]*$/;
const rules = {
    terminalNum: [{ required: true, message: '必填' }, { pattern: pattern2, message: '请输入大于0的整数' }],
    firstAmount: [{ required: true, message: '必填' }, { pattern: pattern, message: '请输入大于或等于0的数值' }],
    allAmount: [{ required: true, message: '必填' }, { pattern: pattern, message: '请输入大于或等于0的数值' }]
}

export default {
    name: "PaymentApply",
    data() {
        return {
            applyInfo: {
                firstAmount: '', //首付金额 条件：大于等于0 首付金额不得大于总金额
                allAmount: '', //总金额
                terminalNum: '', //台数 条件：只可以填写整数大于0
                remark: "" //备注必填
            }
        };
    },
    onReady() {
        this.$refs.uForm.setRules(rules);
    },
    methods: {
        submit() {
            this.$refs.uForm.validate(valid => {
                if (valid) {
                    applyLoansOrder(this.applyInfo).then(res => {
                        if (res.code == "00") {
                            this.applyInfo = this.$options.data().applyInfo;
                            uni.showToast({
                                title: '申请已提交',
                                icon: 'none'
                            });
                        }
                    })
                }
            })
        },
    },
};
</script>

<style lang="less" scoped>
#paymentApply {
    padding-top: 20rpx;
    .u-form {
        section {
            padding: 0 30rpx;
            background-color: #fff;
        }
        .custom-button {
            margin-top: 100px;
        }
    }
}
</style>