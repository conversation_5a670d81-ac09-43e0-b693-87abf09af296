<template>
  <div id="channelPaymentBill">
    <main>
      <section>
        <no-content v-if="show" />

        <view v-show="!show" class="list-data">
          <section v-for="(i, index) in listData" :key="index">
            <p>
              <span>服务商名称</span> <span>{{ i.agentName }}</span>
            </p>
            <p>
              <span>服务商编号</span> <span>{{ i.agentCode }}</span>
            </p>
            <p>
              <span>总金额(元)</span> <span>{{ i.amount | toDecimal2 }}</span>
            </p>
            <p>
              <span>剩余金额(元)</span> <span>{{ i.remainAmount | toDecimal2 }}</span>
            </p>
            <p>
              <span>分润还款比例(%)</span> <span>{{ i.shareRate }}</span>
            </p>
            <p>
              <span>返现还款比例(%)</span> <span>{{ i.cashbackRate }}</span>
            </p>
            <p class="about-time">
              <span>{{ i.createTime | dateFormat }}</span> <span :class="'loansType' + i.loansStatus">{{ i.loansStatus == 0 ? '未还清' : i.loansStatus == 1 ? '已还清' : '' }}</span>
            </p>
            <div @click="openSetRate(i.agentCode)"><span>设置比例</span></div>
          </section>
        </view>

        <u-loadmore v-if="!show" :status="status" @loadmore="loadmore" />
      </section>
    </main>

    <!-- 设置还款比例弹框 -->
    <u-modal ref="uModal" v-model="showSetRate" show-cancel-button title="设置还款比例(%)" @confirm="updateRate" async-close>
      <u-form :model="updateInfo" ref="uForm" class="u-form" label-width="auto">
        <section>
          <u-form-item label="服务商编号">
            <u-input v-model="updateInfo.agentCode" diasbled />
          </u-form-item>
          <u-form-item label="分润还款比例" prop="shareRate">
            <u-input type="number" v-model="updateInfo.shareRate" placeholder="请输入分润还款比例" />
          </u-form-item>
          <u-form-item label="返现还款比例" prop="cashbackRate">
            <u-input type="number" v-model="updateInfo.cashbackRate" placeholder="请输入返现还款比例" />
          </u-form-item>
        </section>
      </u-form>
    </u-modal>
  </div>
</template>

<script>
import { getAppLoansAccounPageList, updateLoansAccountRate } from '../../../../http/api'
import { dateFormat, toDecimal2 } from '../../../../static/utils/date'

const pattern = /^100$|^(\d|[1-9]\d)$/
const rules = {
  shareRate: [
    { required: true, message: '必填' },
    { pattern, message: '请输入0~100区间的整数' }
  ],
  cashbackRate: [
    { required: true, message: '必填' },
    { pattern, message: '请输入0~100区间的整数' }
  ]
}

export default {
  name: 'ChannelPaymentBill',
  components: {},
  filters: {
    dateFormat,
    toDecimal2
  },
  data() {
    return {
      show: false,
      showSetRate: false,
      listData: [],
      total: null,
      status: 'loading',
      updateInfo: {
        agentCode: '', //服务商编号
        shareRate: '', //分润比例 0-100
        cashbackRate: '' //返现比例 0-100
      }
    }
  },
  onLoad() {
    this.getListData()
  },
  onReachBottom() {
    this.loadmore()
  },
  methods: {
    openSetRate(agentCode) {
      this.updateInfo = {
        agentCode: agentCode, //服务商编号
        shareRate: '', //分润比例 0-100
        cashbackRate: '' //返现比例 0-100
      }
      this.showSetRate = true
      this.$nextTick(() => {
        this.$refs.uForm.setRules(rules)
        this.$refs.uForm.resetFields()
      })
    },
    updateRate() {
      this.$refs.uModal.clearLoading()
      this.$refs.uForm.validate(valid => {
        if (valid) {
          updateLoansAccountRate(this.updateInfo).then(res => {
            if (res.code == '00') {
              this.showSetRate = false
              this.getListData()
              uni.showToast({
                title: '设置成功',
                icon: 'none'
              })
            }
          })
        }
      })
    },
    getListData() {
      getAppLoansAccounPageList({
        pageNo: 1,
        pageSize: 20
      }).then(res => {
        if (res.code == '00') {
          this.total = res.data.total
          if (res.data.list.length != 0) {
            this.show = false
            this.listData = res.data.list
            uni.pageScrollTo({
              scrollTop: 0
            })
            if (this.listData.length >= this.total) {
              // 数据全部加载完成
              this.status = 'nomore'
            } else {
              this.status = 'loadmore'
            }
          } else {
            this.show = true
          }
        }
      })
    },
    loadmore() {
      if (this.status == 'nomore') return
      this.status = 'loading'
      getAppLoansAccounPageList({
        pageNo: this.listData.length / 20 + 1,
        pageSize: 20
      }).then(res => {
        if (res.code == '00') {
          this.total = res.data.total
          res.data.list.forEach(i => {
            this.listData.push(i)
          })
          if (this.listData.length >= this.total) {
            // 数据全部加载完成
            this.status = 'nomore'
          } else {
            this.status = 'loadmore'
          }
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
#channelPaymentBill {
  padding-top: 20rpx;
  > main {
    padding: 0 20rpx;
    > section {
      > .list-data {
        width: 100%;
        overflow-x: hidden;
        > section {
          position: relative;
          padding: 20rpx 30rpx;
          background-color: #fff;
          margin-bottom: 20rpx;
          border-radius: 20rpx;
          > p {
            display: flex;
            margin: 6rpx 0;
            color: #666;
            &:not(:nth-last-of-type(2)) {
              > span {
                &:first-of-type {
                  width: 9em;
                  color: rgb(134, 134, 134);
                  font-size: 26rpx;
                }
              }
            }
            .loansType0 {
              color: #004ea9;
            }
            .loansType1 {
              color: green;
            }
          }
          .about-time {
            justify-content: space-between;
            font-size: 26rpx;
            color: #ccc;
          }
          > div {
            position: absolute;
            right: 30rpx;
            top: 20rpx;
            padding: 4rpx 6rpx;
            border-radius: 8rpx;
            background-color: orange;
            > span {
              margin-top: 20rpx;
              color: #fff;
              font-size: 24rpx;
            }
          }
          &:last-of-type {
            margin-bottom: 0;
          }
        }
      }
    }
  }
  .u-form {
    section {
      padding: 0 30rpx;
    }
  }
}
</style>