<template>
    <div id="paymentBill">
        <main>
            <u-sticky>
                <view class="header">
                    <view class="payment-history">
                        <text @click="paymentHistory">查看历史</text>
                    </view>

                    <option-tab :top="60" opts="我的,服务商" :current="currentTab" @select="select" />

                    <section>
                        <div>
                            <p>总金额(元)</p>
                            <p>{{ loansAmount.amount | toDecimal2 }}</p>
                        </div>
                        <div>
                            <p>剩余金额(元)</p>
                            <p>{{ loansAmount.remainAmount | toDecimal2 }}</p>
                        </div>
                    </section>

                </view>
            </u-sticky>

            <section>
                <no-content v-if="show" />

                <view class="list-data" v-if="!show">
                    <section v-for="(i, index) in listData" :key="index">
                        <p class="fix-width"> <span>剩余金额(元)</span> <span>{{ i.debtAmount | toDecimal2 }}</span> </p>
                        <p class="fix-width"> <span>已付金额(元)</span> <span>{{ i.firstAmount | toDecimal2 }}</span> </p>
                        <p class="fix-width"> <span>服务商名称</span> <span>{{ i.agentName }}</span> </p>
                        <p class="about-time"><span>{{ i.applyTime | dateFormat }}</span> <span v-if="!(currentTab == 1 && i.applyType == 0)" :class="'applyType'+i.applyType">{{ i.applyType == 0?"审批中" :i.applyType == 1? "已通过":i.applyType == 2 ?'未通过':'' }}</span> <span class="sheHeBtn" @click="openExamine(i.id)" v-if="i.applyType == 0 && currentTab == 1">审批</span> </p>
                        <p class="remark" v-if="i.remark"> <span>备注:</span> <span>{{ i.remark }}</span> </p>
                    </section>
                </view>

                <u-loadmore v-if="!show" :status="status" @loadmore="loadmore" />

            </section>
        </main>

        <footer class="custom-button">
            <button @click="operation">{{ currentTab == 0 ? '申请' : '渠道账单' }}</button>
        </footer>

        <!-- 审批弹框 -->
        <u-modal v-model="showExamine" show-cancel-button title="审批" @confirm="doExamine">
            <u-radio-group v-model="examineInfo.applyType">
                <u-radio :name="1">通过</u-radio>
                <u-radio :name="2">不通过</u-radio>
            </u-radio-group>
        </u-modal>
    </div>
</template>



<script>
import { getLoansOrderPageList, getLoansAccountAmount, applyUpdateOrder } from "../../../../http/api";
import OptionTab from "../../../../components/OptionTab.vue";
import { dateFormat, toDecimal2 } from "../../../../static/utils/date";

export default {
    name: "PaymentBill",
    filters: {
        dateFormat,
        toDecimal2
    },
    components: {
        OptionTab
    },
    data() {
        return {
            show: false,
            currentTab: 0,
            loansAmount: { amount: '0', remainAmount: '0' },
            showExamine: false,
            listData: [],
            examineInfo: {
                id: '',
                applyType: 1
            },
            total: null,
            status: 'loading'
        };
    },
    onLoad() {
        this.init();
    },
    onReachBottom() {
        this.loadmore()
    },
    methods: {
        openExamine(id) {
            this.examineInfo = {
                id: id,
                applyType: 1
            }
            this.showExamine = true;
        },
        doExamine() {
            applyUpdateOrder(this.examineInfo).then(res => {
                if (res.code == "00") {
                    this.showExamine = false;
                    var examineItem = this.listData.find(item => item.id == this.examineInfo.id);
                    examineItem.applyType = this.examineInfo.applyType;
                    uni.showToast({ title: '审批成功', icon: "none" });
                }
            })

        },
        operation() {
            this.$Router.push({ name: `${this.currentTab == 0 ? 'PaymentApply' : 'ChannelPaymentBill'}` })
        },
        paymentHistory() {
            this.$Router.push({ name: 'PaymentHistory', params: { type: this.currentTab + 1 } })
        },
        select(data) {
            if (this.currentTab == data) return;
            this.currentTab = data;
            this.init();
        },
        init() {
            this.getLoansAmount();
            this.getListData();
        },
        getLoansAmount() {
            getLoansAccountAmount(this.currentTab + 1).then(res => {
                if (res.code == "00") {
                    this.loansAmount = res.data;
                }
            })

        },
        getListData() {
            this.status = 'loading'
            getLoansOrderPageList({
                type: this.currentTab + 1,
                pageNo: 1,
                pageSize: 20,
            }).then((res) => {
                if (res.code == "00") {
                    this.total = res.data.total;
                    if (res.data.list.length != 0) {
                        this.show = false;
                        this.listData = res.data.list;

                        if (this.listData.length >= this.total) {
                            // 数据全部加载完成
                            this.status = 'nomore';
                        } else {
                            this.status = 'loadmore';
                        }
                    } else {
                        this.show = true;
                    }
                }
            });
        },
        loadmore() {
            if (this.status == 'nomore') return;
            this.status = 'loading'
            getLoansOrderPageList({
                type: this.currentTab + 1,
                pageNo: this.listData.length / 20 + 1,
                pageSize: 20,
            }).then((res) => {
                if (res.code == "00") {
                    this.total = res.data.total;
                    res.data.list.forEach((i) => {
                        this.listData.push(i);
                    });
                    if (this.listData.length >= this.total) {
                        // 数据全部加载完成
                        this.status = 'nomore';
                    } else {
                        this.status = 'loadmore';
                    }
                }
            });
        },
    },
};
</script>

<style lang="less" scoped>
#paymentBill {
    height: 100%;
    padding-bottom: 100rpx;
    > main {
        min-height: 100%;
        .header {
            position: relative;
            .payment-history {
                padding: 0 30rpx;
                line-height: 60rpx;
                text-align: right;
                color: #004ea9;
                background-color: #fff;
            }
            section {
                display: flex;
                justify-content: space-around;
                padding-bottom: 10rpx;
                height: 112rpx;
                text-align: center;
                color: #fff;
                background-color: rgb(42, 67, 87);
                p {
                    margin: 10rpx 0;
                    font-size: 26rpx;
                }
            }
            /deep/ header {
                border: none;
                background-color: rgb(42, 67, 87);
                section {
                    color: #fff;
                    background-color: transparent;
                    .checked {
                        color: rgb(12, 165, 211);
                        border-bottom: 1px solid rgb(7, 125, 161);
                        background-color: transparent;
                    }
                }
            }
        }
        > section {
            width: 100vw;
            > .list-data {
                > section {
                    padding: 20rpx 30rpx;
                    border-bottom: 1rpx solid #e5e5e5;
                    background-color: #fff;
                    > p {
                        display: flex;
                        margin: 6rpx 0;
                        color: #666;

                        .sheHeBtn {
                            display: inline-block;
                            background: orange;
                            padding: 4rpx 10rpx;
                            border-radius: 8rpx;
                            color: #fff;
                        }
                        .applyType0 {
                            color: #004ea9;
                        }
                        .applyType1 {
                            color: green;
                        }
                        .applyType2 {
                            color: rgb(206, 7, 7);
                        }
                    }
                    .fix-width {
                        > span {
                            &:first-of-type {
                                width: 88px;
                                color: rgb(134, 134, 134);
                                font-size: 13px;
                            }
                        }
                    }

                    .about-time {
                        justify-content: space-between;
                        font-size: 13px;
                        color: #888;
                    }
                    .remark {
                        > span {
                            font-size: 13px;
                            &:first-of-type {
                                width: 40px;
                                color: rgb(134, 134, 134);
                            }
                        }
                    }
                }
            }
        }
    }
    .custom-button {
        position: fixed;
        bottom: 0;
        > button {
            width: 440rpx;
            background-color: rgb(42, 67, 87);
        }
    }
    /deep/ .u-radio-group {
        display: flex;
        justify-content: space-around;
        padding: 30rpx;
    }
}
</style>