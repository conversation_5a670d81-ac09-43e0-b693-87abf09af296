<template>
    <div id="paymentHistory">
        <main>
            <header>
                <u-sticky>
                    <u-tabs :list="tabList" :current="type" @change="change" />
                </u-sticky>

                <div class="filter-header">
                    <u-dropdown ref="uDropdown" :close-on-click-mask="false" :close-on-click-self="false" @open="openFilterHeader">
                        <u-dropdown-item title="筛选">
                            <view class="filter-form">
                                <u-field v-model="ajaxParams.agentName" label="服务商名称" :label-width="150" placeholder="请输入服务商名称" clearable />
                                <section class="filtrate-time">
                                    <u-field disabled :border-bottom="false" :value="startTime" placeholder="开始时间" @click="showStartPicker = true" />
                                    <span>—</span>
                                    <u-field disabled :border-bottom="false" :value="endTime" placeholder="结束时间" @click="showEndPicker = true" />
                                </section>
                                <div class="btnTools">
                                    <u-button size="medium" @click="toReset">重置</u-button>
                                    <u-button size="medium" color="#004ea9" type="primary" @click="$refs.uDropdown.close(), getListData()">确定</u-button>
                                </div>
                            </view>
                        </u-dropdown-item>
                    </u-dropdown>
                </div>

            </header>
            <section>
                <no-content v-if="show" />

                <view class="list-data" v-show="!show">
                    <section v-for="(i, index) in listData" :key="index">
                        <p> <span>服务商名称</span> <span>{{ i.agentName }}</span> </p>
                        <p> <span>服务商编号</span> <span>{{ i.agentCode }}</span> </p>
                        <p> <span>类型</span> <span>{{ i.changeType == 0 ? "分润还款":i.changeType == 1 ? "返现还款":i.changeType == 2 ? "调增":i.changeType == 3 ? "调减":i.changeType == 4 ? "转入" :"" }}</span> </p>
                        <p> <span>金额(元)</span> <span>{{ i.changeAmount | toDecimal2 }}</span> </p>
                        <p><span>{{ i.createTime | dateFormat }}</span> </p>
                    </section>
                </view>

                <u-loadmore v-if="!show" :status="status" @loadmore="loadmore" />
            </section>
        </main>
        <u-back-top :scrollTop="scrollTop" />

        <u-picker v-model="showStartPicker" title="开始时间" :show-time-tag="false" mode="time" @confirm="onConfirmStart" />
        <u-picker v-model="showEndPicker" title="结束时间" :show-time-tag="false" mode="time" @confirm="onConfirmEnd" />
    </div>
</template>



<script>
import { getLoansAccountHisPageList } from "../../../../http/api";
import { dateFormat, getDate, toDecimal2 } from '../../../../static/utils/date'

export default {
    name: "PaymentHistory",
    components: {
    },
    filters: {
        dateFormat,
        toDecimal2
    },
    data() {
        return {
            show: false,
            type: 0,
            showStartPicker: false,
            showEndPicker: false,
            startTime: dateFormat(new Date(getDate(-6))).substring(0, 10),
            endTime: dateFormat(new Date()).substring(0, 10),
            oldParams: null,
            listData: [],
            total: null,
            status: 'loading',
            ajaxParams: {
                agentName: "", //服务商名称
                changeType: '', //0 分润还款 1 返现还款 2 调增 3 调减  4 货款转入
                type: 1,    // 1 当前服务商 2 下级服务商
                startTime: '', // 开始时间 格式 "yyyy-MM-dd HH:mm:ss" 必填
                endTime: '',    // 结束时间 格式 "yyyy-MM-dd HH:mm:ss" 必填
                pageNo: 1,  //当前页
                pageSize: 20 //条数
            },
            scrollTop: 0,
            tabList: [{ name: '全部' }, { name: "分润" }, { name: "返现" }, { name: "调增" }, { name: "调减" }, { name: "转入" }]
        };
    },
    onLoad() {
        this.ajaxParams.type = this.$Route.query.type;
        this.getListData();
    },
    onPageScroll(e) {
        this.scrollTop = e.scrollTop;
    },
    onReachBottom() {
        this.loadmore()
    },
    methods: {
        openFilterHeader() {
            this.oldParams = JSON.parse(JSON.stringify(this.ajaxParams))
        },
        change(index) {
            this.oldParams = null;
            this.type = index
            this.getListData()
        },
        toReset() {
            this.ajaxParams.agentName = '';
            this.startTime = dateFormat(new Date(getDate(-6))).substring(0, 10);
            this.endTime = dateFormat(new Date()).substring(0, 10);
        },
        getListData() {
            this.ajaxParams.changeType = this.type == 0 ? '' : this.type - 1;
            this.ajaxParams.startTime = this.startTime == "" ? null : this.startTime + " 00:00:00";
            this.ajaxParams.endTime = this.endTime == "" ? null : this.endTime + " 23:59:59";
            this.ajaxParams.pageNo = 1
            if (JSON.stringify(this.oldParams) == JSON.stringify(this.ajaxParams)) {
                return;
            }
            this.status = 'loading'
            getLoansAccountHisPageList(this.ajaxParams).then((res) => {
                if (res.code == "00") {
                    this.total = res.data.total;
                    if (res.data.list.length != 0) {
                        this.show = false;
                        this.listData = res.data.list;
                        uni.pageScrollTo({
                            scrollTop: 0
                        });
                        if (this.listData.length >= this.total) {
                            // 数据全部加载完成
                            this.status = 'nomore';
                        } else {
                            this.status = 'loadmore';
                        }
                    } else {
                        this.show = true;
                    }
                }
            });
        },
        loadmore() {
            if (this.status == 'nomore') return;
            this.status = 'loading'
            this.ajaxParams.changeType = this.type == 0 ? '' : this.type - 1;
            this.ajaxParams.startTime = this.startTime == "" ? null : this.startTime + " 00:00:00";
            this.ajaxParams.endTime = this.endTime == "" ? null : this.endTime + " 23:59:59";
            this.ajaxParams.pageNo += 1
            getLoansAccountHisPageList(this.ajaxParams).then((res) => {
                if (res.code == "00") {
                    this.total = res.data.total;
                    res.data.list && res.data.list.forEach((i) => {
                        this.listData.push(i);
                    });
                    if (this.listData.length >= this.total) {
                        // 数据全部加载完成
                        this.status = 'nomore';
                    } else {
                        this.status = 'loadmore';
                    }
                }
            });
        },
        onConfirmStart(val) {
            this.startTime = dateFormat(val.timestamp * 1000).substring(0, 10);
            this.showStartPicker = false;
        },
        onConfirmEnd(val) {
            this.endTime = dateFormat(val.timestamp * 1000).substring(0, 10);
            this.showEndPicker = false;
        },
    },
};
</script>
<style lang="less">
@import "../../../../static/css/game.less";
</style>
<style lang="less" scoped>
#paymentHistory {
    > main {
        position: relative;
        width: 100%;
        > section {
            padding: 20rpx 20rpx 0;
            > .list-data {
                overflow-x: hidden;
                > section {
                    padding: 20rpx 30rpx;
                    margin-bottom: 20rpx;
                    background-color: #fff;
                    border-radius: 10rpx;

                    > p {
                        display: flex;
                        margin: 6rpx 0;
                        color: #666;
                        &:not(:last-of-type) {
                            > span {
                                &:first-of-type {
                                    width: 6.5em;
                                    color: rgb(134, 134, 134);
                                    font-size: 26rpx;
                                }
                            }
                        }

                        &:last-of-type {
                            justify-content: space-between;
                            font-size: 26rpx;
                            color: #ccc;
                        }
                    }
                    &:last-of-type {
                        margin-bottom: 0;
                    }
                }
            }
        }
    }
}
</style>