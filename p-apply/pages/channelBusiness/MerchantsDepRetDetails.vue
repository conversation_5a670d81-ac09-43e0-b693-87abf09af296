<template>
    <div id="merchantsDepRetDetails">
        <main>
            <u-sticky>
                <view class="tabs">
                    <u-tabs :list="tabList" :is-scroll="false" :current="active" @change="change" />
                </view>
            </u-sticky>

            <div class="search-bar">
                <view class="content">
                    <u-field :label-width="50" icon="search" :border-bottom="false" type="text" placeholder="请输入商户号查询" v-model="merchants.merchId" />
                </view>
            </div>

            <no-content v-if="show" />
            <view v-show="!show" class="list-data">
                <section v-for="(r, index) in records" :key="index">
                    <p>
                        <span>商户号</span>
                        <span>{{ r.merchId }}</span>
                    </p>
                    <p>
                        <span>商户名称</span>
                        <span>{{ r.merchName }}</span>
                    </p>
                    <p>
                        <span>商户缴费时间</span>
                        <span>{{
              r.merchPayTime == null ? "--" : dateFormat(r.merchPayTime)
            }}</span>
                    </p>
                    <p>
                        <span>商户缴费结果</span>
                        <span>{{
              r.merchPayResult == 0
                ? "未处理"
                : r.merchPayResult == 1
                ? "成功"
                : "扣除失败"
            }}
                        </span>
                    </p>
                    <p>
                        <span>代理商入账时间</span>
                        <span>{{
              r.agentCollectTime == null ? "--" : dateFormat(r.agentCollectTime)
            }}</span>
                    </p>
                    <p>
                        <span>代理商入账结果</span>
                        <span>{{ r.agentCollectResult == 0 ? "未处理" : r.agentCollectResult == 1 ? "成功" : "入账失败" }}</span>
                    </p>
                    <p>
                        <span>POS服务金额(元)</span>
                        <span>{{ r.posCharge }}</span>
                    </p>
                    <p>
                        <span>VIP会员服务费金额(元)</span>
                        <span>{{ r.vipCharge }}</span>
                    </p>
                    <p>
                        <span>SIM通信服务费金额(元)</span>
                        <span>{{ r.simCharge }}</span>
                    </p>
                    <p>
                        <span>商户已冻结金额(元)</span>
                        <span>{{ r.frozenAmt }}</span>
                    </p>
                </section>
            </view>

            <u-loadmore v-if="!show" :status="status" @loadmore="loadmore" />
        </main>
    </div>
</template>

<script>
import { dateFormat, debounce } from "../../../static/utils/date";
import { getDepRetDetails } from "../../../http/api";

export default {
    name: "MerchantsDepRetDetails",
    data() {
        return {
            merchants: {
                payChannelNo: '',
                merchId: "",
                optNo: null,
                agentOptNo: null,
                merchPayResult: null,
                agentCollectResult: null,
                pageNumber: 1,
                pageSize: 10,
            },
            active: 0,
            show: false,
            records: [],
            dateFormat: dateFormat,
            status: 'loading',
            page: null,
            tabList: [{ name: '全部' }, { name: '未处理' }, { name: '入账成功' }, { name: '入账失败' }]
        };
    },
    onLoad() {
        this.merchants.payChannelNo = this.$Route.query.payChannelNo
        this.getRecordsList();
        this.$watch(
            "merchants.merchId",
            debounce(function () {
                this.getRecordsList();
            }, 700)
        );
    },
    onReachBottom() {
        this.loadmore()
    },
    methods: {
        change(index) {
            this.active = index
            this.getRecordsList()
        },
        changeParamsType() {
            this.merchants.agentCollectResult =
                this.active == 0 ? null : this.active - 1;
        },
        getRecordsList() {
            this.status = 'loading'
            this.changeParamsType();
            if (!this.$Route.query.payChannelNo) return;
            getDepRetDetails(this.merchants).then((res) => {
                if (res.code == "00") {
                    if (res.data.list.length != 0) {
                        this.show = false;
                        this.page = res.data.total;
                        this.records = res.data.list;
                        uni.pageScrollTo({
                            scrollTop: 0
                        });
                        if (this.records.length >= this.page) {
                            // 数据全部加载完成
                            this.status = 'nomore';
                        } else {
                            this.status = 'loadmore';
                        }
                    } else {
                        this.show = true;
                    }
                }
            });
        },
        loadmore() {
            if (this.status == 'nomore') return;
            this.status = 'loading'
            this.changeParamsType();
            getDepRetDetails({
                payChannelNo: this.merchants.payChannelNo,
                merchId: this.merchants.merchId,
                optNo: null,
                agentOptNo: null,
                merchPayResult: null,
                agentCollectResult: this.merchants.agentCollectResult,
                pageNumber:
                    this.records.length % 10 > 0
                        ? Math.ceil(this.records.length / 10)
                        : this.records.length / 10 + 1,
                pageSize: 10,
            }).then((res) => {
                if (res.code == "00") {
                    if (res.data.list.length != 0) {
                        this.show = false;
                        this.page = res.data.total;
                        res.data.list.forEach((i) => {
                            this.records.push(i);
                        });
                    } else {
                        this.show = true;
                    }
                    if (this.records.length >= this.page) {
                        this.status = 'nomore';
                    } else {
                        this.status = 'loadmore';
                    }
                }
            });
        },
    },
};
</script>

<style lang="less" scoped>
#merchantsDepRetDetails {
    main {
        .tabs {
            background-color: #f3f5f7;
            border-bottom: 1rpx solid #e5e5e5;
            /deep/ .u-tabs {
                border-radius: 10rpx;
            }
        }
        .list-data {
            overflow: hidden;
            padding: 0 30rpx;
            > section {
                background-color: #fff;
                padding: 15px;
				
                margin-top: 20rpx;
                border-radius: 16rpx;
                &:nth-of-type(1) {
                    margin-top: 0;
                }
                > p {
                    display: flex;
                    justify-content: space-between;
                    &:nth-of-type(1) {
                        margin-top: 0;
                    }
                    &:last-of-type {
                        margin-bottom: 0;
                    }
                    > span {
                        &:nth-of-type(1) {
                            color: #5f6062;
                        }
                    }
                }
            }
        }
    }
}
</style>