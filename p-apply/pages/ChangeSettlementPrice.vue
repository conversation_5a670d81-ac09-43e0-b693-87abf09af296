<template>
  <div id="changeSettlementPrice">
    <main>
      <u-field label="代理商" label-width="170" placeholder="请选择下级代理商" v-model="agent">
        <u-icon slot="right" name="search" size="40" @click="showAgents" />
      </u-field>
      <u-select v-model="agentsPicker" mode="single-column" :list="agents" value-name="val" label-name="val" @confirm="agentConfirm" />

      <u-field disabled label="结算价模板" label-width="170" right-icon="arrow-down-fill" placeholder="请选择结算价模板" :value="settlePrice" @click="showSettlePrices" />
      <u-select v-model="settlePricesPicker" mode="single-column" :list="settlePrices" @confirm="settlePriceConfirm" />

      <div>
        <h4>结算底价设置<span>（小数点后最多3位）</span></h4>
      </div>
      <section>
        <u-form :model="cost" ref="uForm" class="u-form">
          <u-form-item label="借记卡(%)" prop="debitCost">
            <u-input type="digit" v-model="cost.debitCost" :disabled="readonly" :clearable="false" placeholder-style="font-size:24rpx" />
          </u-form-item>
          <u-form-item label="借记卡封顶值(元)" prop="debitCapValue">
            <u-input type="digit" v-model="cost.debitCapValue" :disabled="readonly" :clearable="false" placeholder-style="font-size:24rpx" />
          </u-form-item>
          <p class="interval" />
          <u-form-item label="信用卡(%)" prop="creditCost">
            <u-input type="digit" v-model="cost.creditCost" :disabled="readonly" :clearable="false" placeholder-style="font-size:24rpx" />
          </u-form-item>
          <u-form-item v-if="showCreditDiscountCost" label="信用卡特惠(%)" prop="creditDiscountCost">
            <u-input type="digit" v-model="cost.creditDiscountCost" :disabled="readonly" :clearable="false" placeholder-style="font-size:24rpx" />
          </u-form-item>
          <u-form-item v-if="showCreditSecTransCost" label="信用卡秒到交易(%)" prop="creditSecTransCost">
            <u-input type="digit" v-model="cost.creditSecTransCost" :disabled="readonly" :clearable="false" placeholder-style="font-size:24rpx" />
          </u-form-item>
          <p class="interval" />
          <u-form-item label="扫码(%)" prop="scanCost">
            <u-input type="digit" v-model="cost.scanCost" :disabled="readonly" :clearable="false" placeholder-style="font-size:24rpx" />
          </u-form-item>
          <u-form-item label="闪付(%)" prop="passCost">
            <u-input type="digit" v-model="cost.passCost" :disabled="readonly" :clearable="false" placeholder-style="font-size:24rpx" />
          </u-form-item>
        </u-form>
      </section>
    </main>

    <footer class="custom-button">
      <button @click="submit">提 交</button>
    </footer>
  </div>
</template>

<script>
import { getDirRealAgent, getAgentRateType, getSettlementPrice, changeSettmentPrice } from '../../http/api'

const pattern = /^0\.\d{0,3}$/
const costs = ['creditCost', 'debitCost', 'scanCost', 'passCost', 'creditDiscountCost', 'creditSecTransCost']
const rules = {
  debitCapValue: [{ required: true, message: '必填' }]
}
costs.forEach(c => (rules[c] = [{ pattern, message: '大于0、小于1且不得超过3位小数' }]))

export default {
  name: 'ChangeSettlementPrice',
  data() {
    return {
      agentsPicker: false,
      agents: [],
      agent: '',
      agentCode: '',
      settlePricesPicker: false,
      settlePrices: [],
      settlePrice: '',
      rateType: '',
      cost: {
        creditCost: '0.000',
        debitCost: '0.000',
        debitCapValue: '0',
        scanCost: '0.000',
        passCost: '0.000',
        creditDiscountCost: '0.000',
        creditSecTransCost: '0.000'
      },
      showCreditDiscountCost: false, // 是否展示信用卡特惠
      showCreditSecTransCost: false // 是否展示信用卡秒到交易成本
    }
  },
  onReady() {
    this.$refs.uForm.setRules(rules)
  },
  methods: {
    showAgents() {
      this.agents = []
      getDirRealAgent(this.agent).then(res => {
        if (res.code == '00') {
          if (res.data.length != 0) {
            res.data.forEach(i => {
              this.agents.push({ val: i.name + '（' + i.agentCode + '）' })
            })
            this.agentsPicker = true
          } else {
            uni.showToast({
              title: '暂无下级代理商！',
              icon: 'none'
            })
          }
        }
      })
    },
    agentConfirm(val) {
      const value = val[0].value
      const bracketStart = value.lastIndexOf('（')
      const bracketEnd = value.lastIndexOf('）')

      this.agent = value.substring(0, bracketStart)
      this.agentCode = value.substring(bracketStart + 1, bracketEnd)

      getAgentRateType().then(res => {
        if (res.code == '00') {
          this.settlePrices = []
          res.data.forEach(i => {
            this.settlePrices.push({ label: i.modelName, value: i.rateType })
          })
          this.agentsPicker = false
        }
      })
    },
    showSettlePrices() {
      if (this.agentCode != '') {
        this.settlePricesPicker = true
      } else {
        uni.showToast({ title: '未选择代理商！', icon: 'none' })
      }
    },
    settlePriceConfirm(val) {
      this.settlePrice = val[0].label
      this.rateType = val[0].value

      getSettlementPrice(this.agentCode, this.rateType).then(res => {
        if (res.code == '00') {
          this.cost = {
            creditCost: res.data.creditCost,
            debitCost: res.data.debitCost,
            debitCapValue: res.data.debitCapValue,
            scanCost: res.data.scanCost,
            passCost: res.data.passCost,
            creditDiscountCost: res.data.creditDiscountCost,
            creditSecTransCost: res.data.creditSecTransCost ? res.data.creditSecTransCost : '0.000'
          }
          const { creditDiscountOpenConf, secTransOpenConf } = res.data
          this.showCreditDiscountCost = creditDiscountOpenConf
          this.showCreditSecTransCost = secTransOpenConf

          this.settlePricesPicker = false
        }
      })
    },
    submit() {
      if (this.agentCode != '' && this.settlePrice != '') {
        this.$refs.uForm.validate(valid => {
          if (valid) {
            var cost = {
              agentCode: this.agentCode,
              loginAgentCode: this.$store.state.userInfo.agentCode,
              rateType: this.rateType,
              creditCost: this.cost.creditCost,
              debitCost: this.cost.debitCost,
              debitCapValue: this.cost.debitCapValue,
              scanCost: this.cost.scanCost,
              passCost: this.cost.passCost,
              creditDiscountCost: this.cost.creditDiscountCost,
              creditSecTransCost: this.cost.creditSecTransCost
            }
            changeSettmentPrice(cost).then(res => {
              if (res.code == '00') {
                uni.showToast({
                  title: res.message,
                  icon: 'none'
                })
              }
            })
          }
        })
      } else {
        uni.showToast({
          title: this.agentCode == '' ? '未选择代理商！' : '未选择结算价模板！',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
#changeSettlementPrice {
  padding-top: 20rpx;
  main {
    background-color: #fff;
    > div {
      padding: 2vw 30rpx;
      background-color: #f3f5f7;
      > h4 {
        margin: 0;
        color: #222222;
        font-weight: 500;
        font-size: 24rpx;
        span {
          font-size: 24rpx;
          font-weight: 400;
          color: #222222;
          opacity: 0.8;
        }
      }
    }
    > section {
      /deep/ .u-form {
        .u-form-item {
          padding: 20rpx 30rpx;
        }
        .u-form-item--left {
          flex: 1 !important;
        }
        .u-form-item--right {
          flex: none;
          width: 140rpx;
          border-radius: 6rpx;
          background: #eaeef1;
          input {
            text-align: center;
            color: #4e77d9;
          }
        }
        .u-form-item__message {
          text-align: right;
        }
      }
    }
  }
  .custom-button {
    margin-top: 40rpx;
  }
}
</style>