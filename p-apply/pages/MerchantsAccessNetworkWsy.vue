<template>
    <div id="merchantsAccessNetworkWsy">
        <main>
            <div class="desc-img">
                <u-image width="100%" v-for="(url, key) in descImgs" :key="key" :src="url" mode="widthFix" />
            </div>

            <div class="qrCard">
                <canvas v-if="!imageUrl" style="width: 100%; height: 1000px; visibility: hidden" canvas-id="qrImage" id="qrImage"></canvas>
                <image  v-else :src="imageUrl" mode="widthFix" @longpress="openAppToolBox(imageUrl)" show-menu-by-longpress />

                <div class="qrCard-content" v-if="qrUrl && !imageUrl" style="visibility: hidden">
                    <create-qrcode :val="qrUrl" :size="340" loadMake @result="resultQr" :showLoading="false" />
                </div>
            </div>
        </main>
    </div>
</template>

<script>
import CreateQrcode from 'tki-qrcode'
import { getMerchantsUrlList, getSystemParamImgUrl } from '../../http/api'

export default {
    name: 'MerchantsAccessNetworkWsy',
    components: { CreateQrcode },
    data() {
        return {
            qrUrl: '',
            imageUrl: '',
            descImgs: []
        }
    },
    onLoad() {
        this.getQrUrl()
        this.getDescImgs()
    },
    methods: {
        draw(qrImgUrl) {
            const ctx = uni.createCanvasContext('qrImage')
            const { windowWidth } = uni.getSystemInfoSync()
            uni.getImageInfo({
                src: '../static/images/wsy-saas-bg.png',
                success: ({ width, height }) => {
                    // 背景
                    ctx.drawImage(require('../static/images/wsy-saas-bg.png'), 0, 0, windowWidth, (height * windowWidth) / width)
                    //二维码
                    ctx.drawImage(qrImgUrl, windowWidth / 2 - uni.upx2px(340) / 2, uni.upx2px(480), uni.upx2px(340), uni.upx2px(340))
                    ctx.draw(false, () => {
                        uni.canvasToTempFilePath({
                            x: 0,
                            y: 0,
                            width: windowWidth,
                            height: (height * windowWidth) / width,
                            canvasId: 'qrImage',
                            success: res => {
                                this.imageUrl = res.tempFilePath
                            },
                            fail: err => {
                                console.log('error', err)
                            }
                        })
                    })
                }
            })
        },
        getQrUrl() {
            getMerchantsUrlList('0020').then(res => {
                if (res.code == '00') {
                    if (res.data.length != 0) {
                        this.qrUrl = res.data[0]['url']
                    } else {
                        this.$u.toast('无可用于生成二维码的链接!')
                    }
                }
            })
        },
        getDescImgs() {
            getSystemParamImgUrl({ tip: '占位' }).then(res => {
                if (res.code == '00') {
                    this.descImgs = res.data.wsyRegiDisplayImgUrl || []
                }
            })
        },
        resultQr(qrImgUrl) {
            this.$nextTick(() => {
                this.draw(qrImgUrl)
            })
        },
        openAppToolBox(imgPath) {
            // #ifdef APP-PLUS
            uni.showActionSheet({
                itemList: ['保存', '分享'],
                success: function ({ tapIndex }) {
                    switch (tapIndex) {
                        case 0:
                            uni.saveImageToPhotosAlbum({
                                filePath: imgPath,
                                success: function () {
                                    uni.showToast({ title: '图片保存成功' })
                                }
                            })
                            break
                        case 1:
                            plus.share.sendWithSystem(
                                { pictures: [imgPath] },
                                function () {
                                    console.log('分享成功')
                                },
                                function (e) {
                                    console.log(e)
                                }
                            )
                            break
                        default:
                            break
                    }
                }
            })
            // #endif
        }
    }
}
</script>

<style lang="less" scoped>
#merchantsAccessNetworkWsy {
    main {
        .desc-img {
            font-size: 0;
        }

        .qrCard {
            width: 100%;
            font-size: 0;
            > image {
                width: 100%;
            }
        }
    }
}
</style>
