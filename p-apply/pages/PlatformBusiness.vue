<template>
    <div id="platformBusiness">
        <u-cell-group :border="false">
            <u-cell-item v-if="state.currentMode == 1" title="终端采购款记录" @click="toLink('PaymentBill')" />
            <u-cell-item v-if="state.currentMode == 2 && state.userInfo.agentLevel != 1" title="终端划拨" @click="toLink('DirectStir')" />
            <u-cell-item v-if="state.currentMode == 1" title="附加返现政策" @click="toLink('CashBackModel')" />
            <u-cell-item title="附加返现订单" @click="toLink('MerchantWithdrawalFee')" />
        </u-cell-group>
    </div>
</template>

<script>
export default {
    name: "PlatformBusiness",
    computed: {
        state() {
            return this.$store.state
        }
    },
    methods: {
        toLink(name) {
            this.$Router.push({ name });
        }
    },
};
</script>

<style lang="less" scoped>
#platformBusiness {
    padding-top: 20rpx;
}
</style>