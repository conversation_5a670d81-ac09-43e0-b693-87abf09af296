/**
 * 现代化请求管理器
 * 基于Promise的HTTP请求封装，支持拦截器、重试、缓存等功能
 */

import { storage } from '../static/utils/modernUtils.js'

class ModernRequest {
  constructor(config = {}) {
    this.config = {
      baseURL: '',
      timeout: 10000,
      retryCount: 3,
      retryDelay: 1000,
      enableCache: false,
      cacheTime: 5 * 60 * 1000, // 5分钟
      ...config
    }
    
    // 请求拦截器
    this.requestInterceptors = []
    // 响应拦截器
    this.responseInterceptors = []
    // 错误拦截器
    this.errorInterceptors = []
    // 缓存存储
    this.cache = new Map()
  }
  
  /**
   * 添加请求拦截器
   * @param {Function} fulfilled 成功回调
   * @param {Function} rejected 失败回调
   */
  addRequestInterceptor(fulfilled, rejected) {
    this.requestInterceptors.push({ fulfilled, rejected })
  }
  
  /**
   * 添加响应拦截器
   * @param {Function} fulfilled 成功回调
   * @param {Function} rejected 失败回调
   */
  addResponseInterceptor(fulfilled, rejected) {
    this.responseInterceptors.push({ fulfilled, rejected })
  }
  
  /**
   * 添加错误拦截器
   * @param {Function} handler 错误处理函数
   */
  addErrorInterceptor(handler) {
    this.errorInterceptors.push(handler)
  }
  
  /**
   * 生成缓存键
   * @param {string} url 请求URL
   * @param {object} data 请求数据
   * @param {string} method 请求方法
   * @returns {string} 缓存键
   */
  generateCacheKey(url, data, method) {
    return `${method}:${url}:${JSON.stringify(data || {})}`
  }
  
  /**
   * 获取缓存
   * @param {string} key 缓存键
   * @returns {any} 缓存数据
   */
  getCache(key) {
    const cached = this.cache.get(key)
    if (cached && Date.now() - cached.timestamp < this.config.cacheTime) {
      return cached.data
    }
    this.cache.delete(key)
    return null
  }
  
  /**
   * 设置缓存
   * @param {string} key 缓存键
   * @param {any} data 缓存数据
   */
  setCache(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }
  
  /**
   * 执行拦截器
   * @param {Array} interceptors 拦截器数组
   * @param {any} data 数据
   * @returns {Promise} 处理后的数据
   */
  async executeInterceptors(interceptors, data) {
    let result = data
    for (const interceptor of interceptors) {
      try {
        if (interceptor.fulfilled) {
          result = await interceptor.fulfilled(result)
        }
      } catch (error) {
        if (interceptor.rejected) {
          result = await interceptor.rejected(error)
        } else {
          throw error
        }
      }
    }
    return result
  }
  
  /**
   * 处理错误
   * @param {Error} error 错误对象
   * @param {object} config 请求配置
   */
  async handleError(error, config) {
    // 执行错误拦截器
    for (const handler of this.errorInterceptors) {
      try {
        await handler(error, config)
      } catch (e) {
        console.error('Error interceptor failed:', e)
      }
    }
    
    // 根据错误类型进行处理
    if (error.statusCode) {
      switch (error.statusCode) {
        case 401:
          // 未授权，清除token并跳转登录
          storage.remove('token')
          uni.reLaunch({
            url: '/pages/login/Login'
          })
          break
        case 403:
          uni.showToast({
            title: '权限不足',
            icon: 'none'
          })
          break
        case 404:
          uni.showToast({
            title: '请求的资源不存在',
            icon: 'none'
          })
          break
        case 500:
          uni.showToast({
            title: '服务器内部错误',
            icon: 'none'
          })
          break
        default:
          uni.showToast({
            title: error.errMsg || '请求失败',
            icon: 'none'
          })
      }
    } else {
      uni.showToast({
        title: '网络连接失败',
        icon: 'none'
      })
    }
  }
  
  /**
   * 发送请求
   * @param {object} options 请求选项
   * @returns {Promise} 请求结果
   */
  async request(options) {
    // 合并配置
    const config = {
      ...this.config,
      ...options,
      url: (options.url.startsWith('http') ? '' : this.config.baseURL) + options.url
    }
    
    // 检查缓存
    if (config.enableCache && config.method === 'GET') {
      const cacheKey = this.generateCacheKey(config.url, config.data, config.method)
      const cached = this.getCache(cacheKey)
      if (cached) {
        return cached
      }
    }
    
    // 执行请求拦截器
    const processedConfig = await this.executeInterceptors(this.requestInterceptors, config)
    
    // 发送请求（带重试机制）
    let lastError
    for (let i = 0; i <= this.config.retryCount; i++) {
      try {
        const response = await new Promise((resolve, reject) => {
          uni.request({
            ...processedConfig,
            success: resolve,
            fail: reject
          })
        })
        
        // 执行响应拦截器
        const processedResponse = await this.executeInterceptors(this.responseInterceptors, response)
        
        // 设置缓存
        if (config.enableCache && config.method === 'GET') {
          const cacheKey = this.generateCacheKey(config.url, config.data, config.method)
          this.setCache(cacheKey, processedResponse)
        }
        
        return processedResponse
      } catch (error) {
        lastError = error
        
        // 如果不是最后一次重试，等待后继续
        if (i < this.config.retryCount) {
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay))
          continue
        }
        
        // 处理错误
        await this.handleError(error, config)
        throw error
      }
    }
  }
  
  /**
   * GET请求
   * @param {string} url 请求URL
   * @param {object} params 请求参数
   * @param {object} config 请求配置
   * @returns {Promise} 请求结果
   */
  get(url, params, config = {}) {
    return this.request({
      url,
      method: 'GET',
      data: params,
      ...config
    })
  }
  
  /**
   * POST请求
   * @param {string} url 请求URL
   * @param {object} data 请求数据
   * @param {object} config 请求配置
   * @returns {Promise} 请求结果
   */
  post(url, data, config = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...config
    })
  }
  
  /**
   * PUT请求
   * @param {string} url 请求URL
   * @param {object} data 请求数据
   * @param {object} config 请求配置
   * @returns {Promise} 请求结果
   */
  put(url, data, config = {}) {
    return this.request({
      url,
      method: 'PUT',
      data,
      ...config
    })
  }
  
  /**
   * DELETE请求
   * @param {string} url 请求URL
   * @param {object} params 请求参数
   * @param {object} config 请求配置
   * @returns {Promise} 请求结果
   */
  delete(url, params, config = {}) {
    return this.request({
      url,
      method: 'DELETE',
      data: params,
      ...config
    })
  }
  
  /**
   * 上传文件
   * @param {string} url 上传URL
   * @param {string} filePath 文件路径
   * @param {object} formData 表单数据
   * @param {object} config 配置
   * @returns {Promise} 上传结果
   */
  upload(url, filePath, formData = {}, config = {}) {
    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url: (url.startsWith('http') ? '' : this.config.baseURL) + url,
        filePath,
        name: 'file',
        formData,
        ...config,
        success: resolve,
        fail: reject
      })
    })
  }
  
  /**
   * 下载文件
   * @param {string} url 下载URL
   * @param {object} config 配置
   * @returns {Promise} 下载结果
   */
  download(url, config = {}) {
    return new Promise((resolve, reject) => {
      uni.downloadFile({
        url: (url.startsWith('http') ? '' : this.config.baseURL) + url,
        ...config,
        success: resolve,
        fail: reject
      })
    })
  }
}

// 创建默认实例
const modernRequest = new ModernRequest({
  baseURL: process.env.NODE_ENV === 'development' ? 'http://localhost:3000' : 'https://api.example.com',
  timeout: 10000
})

// 添加默认请求拦截器
modernRequest.addRequestInterceptor(
  (config) => {
    // 添加token
    const token = storage.get('token')
    if (token) {
      config.header = {
        ...config.header,
        'Authorization': `Bearer ${token}`
      }
    }
    
    // 添加公共参数
    config.header = {
      'Content-Type': 'application/json',
      ...config.header
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 添加默认响应拦截器
modernRequest.addResponseInterceptor(
  (response) => {
    // 统一处理响应数据
    if (response.statusCode === 200) {
      return response.data
    } else {
      throw new Error(response.errMsg || '请求失败')
    }
  },
  (error) => {
    return Promise.reject(error)
  }
)

export default modernRequest
