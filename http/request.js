import store from '../store';
import { generateSign } from '../static/utils/sign';

// const BASE_URL = 'http://192.168.1.152:9485/api'; // 联调环境
const BASE_URL = 'https://openapiapp.shtcloud.com/app'; // 生产环境

// 请求计数器
let requestCount = 0;

const request = (options) => {
  const httpOptions = {
    url: BASE_URL + options.url,
    data: options.data || {},
    method: options.method.toUpperCase(),
    header: {
      'X-Requested-With': 'XMLHttpRequest',
      'Content-Type': 'application/json; charset=UTF-8',
      _agent_token: store.state.token || options.token || '',
      _app_sign: generateSign(
        options.data,
        options.method.toLowerCase() === 'get'
      )
    }
  };

  return new Promise((resolve, reject) => {
    // 显示 loading
    if (requestCount === 0) {
      uni.showLoading({ title: '加载中', mask: true });
    }
    requestCount++;

    uni
      .request(httpOptions)
      .then((response) => {
        const res = response[1].data;
        if (res.code && res.code !== '00') {
          if (res.code === '0103') {
            uni.$u.throttle(() => {
              uni.showModal({
                showCancel: false,
                confirmText: '重新登录',
                content: '身份验证已过期，请重新登录!',
                success: (res2) => {
                  if (res2.confirm) store.dispatch('login_out');
                }
              });
            }, 2000);
          } else {
            uni.showModal({
              showCancel: false,
              content: res.message || '请求失败'
            });
          }
        }
        resolve(res);
      })
      .catch((error) => {
        uni.showToast({
          title: '出错了，请稍后重试!',
          mask: true,
          icon: 'none'
        });
        reject(error);
      })
      .finally(() => {
        // 关闭 loading
        requestCount--;
        if (requestCount <= 0) {
          requestCount = 0;
          setTimeout(() => uni.hideLoading(), 200); // 延迟关闭
        }
      });
  });
};

export default request;
