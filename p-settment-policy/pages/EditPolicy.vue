<template>
  <div id="changeSettlementPolicy">
    <main>
      <div>
        <h4>基本信息</h4>
      </div>
      <header>
        <div>
          <p>
            政策名称:<span>{{ allInfo.modelName }}</span>
          </p>
          <span v-if="allInfo.type == 0" @click="handleEditName">编辑</span>
        </div>
        <p>
          代理名称: <span>{{ allInfo.agentName }}</span>
        </p>
        <p>
          代理编号: <span>{{ allInfo.agentCode }}</span>
        </p>
        <p>
          创建时间: <span>{{ allInfo.createTime | dateFormat }}</span>
        </p>
        <section v-if="allInfo.subChannelStatus === 1">
          <u-tag text="聚合" type="error" size="mini" />
        </section>
      </header>
      <div>
        <h4>政策设置<span>（小数点后最多3位）</span></h4>
      </div>
      <section>
        <u-form :model="form" ref="uForm" class="u-form">
          <section v-if="allInfo.subChannelStatus === 0">
            <u-form-item label="借记卡(%)" prop="debitCost" :target="form.cost">
              <u-input
                type="digit"
                v-model="form.cost.debitCost"
                :clearable="false"
                placeholder-style="font-size:24rpx"
              />
            </u-form-item>
            <u-form-item
              label="借记卡封顶值(元)"
              prop="debitCapValue"
              :target="form.cost"
            >
              <u-input
                type="number"
                v-model="form.cost.debitCapValue"
                :clearable="false"
                placeholder-style="font-size:24rpx"
              />
            </u-form-item>
            <p class="interval" />
            <u-form-item
              label="信用卡(%)"
              prop="creditCost"
              :target="form.cost"
            >
              <u-input
                type="digit"
                v-model="form.cost.creditCost"
                :clearable="false"
                placeholder-style="font-size:24rpx"
              />
            </u-form-item>
            <u-form-item
              v-if="userInfo.showCreditDiscountOpenConf"
              label="信用卡特惠(%)"
              prop="creditDiscountCost"
              :target="form.cost"
            >
              <u-input
                type="digit"
                v-model="form.cost.creditDiscountCost"
                :clearable="false"
                placeholder="选填"
                placeholder-style="font-size:24rpx"
              />
            </u-form-item>
            <u-form-item
              v-if="userInfo.showSecTransOpenConf"
              label="信用卡秒到交易(%)"
              prop="creditSecTransCost"
              :target="form.cost"
            >
              <u-input
                type="digit"
                v-model="form.cost.creditSecTransCost"
                :clearable="false"
                placeholder-style="font-size:24rpx"
              />
            </u-form-item>
            <p class="interval" />
            <u-form-item label="扫码(%)" prop="scanCost" :target="form.cost">
              <u-input
                type="digit"
                v-model="form.cost.scanCost"
                :clearable="false"
                placeholder-style="font-size:24rpx"
              />
            </u-form-item>
            <u-form-item label="闪付(%)" prop="passCost" :target="form.cost">
              <u-input
                type="digit"
                v-model="form.cost.passCost"
                :clearable="false"
                placeholder-style="font-size:24rpx"
              />
            </u-form-item>
            <u-form-item
              label="支付宝大额(%)"
              prop="alipayLargerRate"
              :target="form.cost"
            >
              <u-input
                type="digit"
                v-model="form.cost.alipayLargerRate"
                :clearable="false"
                placeholder="选填"
                placeholder-style="font-size:24rpx"
              />
            </u-form-item>
          </section>

          <template v-if="allInfo.subChannelStatus === 1">
            <template v-if="subChannelList.length">
              <u-tabs
                :list="subChannelList"
                :current="currentSubChannel"
                @change="changeSubChannel"
              ></u-tabs>
              <section
                v-for="(item, key) in form.costList"
                :key="key"
                v-show="currentSubChannel === key"
              >
                <u-form-item label="借记卡(%)" prop="debitCost" :target="item">
                  <u-input
                    type="digit"
                    v-model="item.debitCost"
                    :clearable="false"
                    placeholder-style="font-size:24rpx"
                  />
                </u-form-item>
                <u-form-item
                  label="借记卡封顶值(元)"
                  prop="debitCapValue"
                  :target="item"
                >
                  <u-input
                    type="number"
                    v-model="item.debitCapValue"
                    :clearable="false"
                    placeholder-style="font-size:24rpx"
                  />
                </u-form-item>
                <p class="interval" />
                <u-form-item label="信用卡(%)" prop="creditCost" :target="item">
                  <u-input
                    type="digit"
                    v-model="item.creditCost"
                    :clearable="false"
                    placeholder-style="font-size:24rpx"
                  />
                </u-form-item>
                <p class="interval" />
                <u-form-item label="扫码(%)" prop="scanCost" :target="item">
                  <u-input
                    type="digit"
                    v-model="item.scanCost"
                    :clearable="false"
                    placeholder-style="font-size:24rpx"
                  />
                </u-form-item>
                <u-form-item label="闪付(%)" prop="passCost" :target="item">
                  <u-input
                    type="digit"
                    v-model="item.passCost"
                    :clearable="false"
                    placeholder-style="font-size:24rpx"
                  />
                </u-form-item>
                <u-form-item
                  label="支付宝大额(%)"
                  prop="alipayLargerRate"
                  :target="item"
                >
                  <u-input
                    type="digit"
                    v-model="item.alipayLargerRate"
                    :clearable="false"
                    placeholder="选填"
                    placeholder-style="font-size:24rpx"
                  />
                </u-form-item>
                <!-- <p class="interval" />
                <u-form-item label="无卡费率(贷记)(%)" prop="eposCreditRate" :target="item">
                  <u-input type="digit" v-model="item.eposCreditRate" :clearable="false" placeholder-style="font-size:24rpx" />
                </u-form-item>
                <u-form-item label="无卡费率(借记)(%)" prop="eposDebitRate" :target="item">
                  <u-input type="digit" v-model="item.eposDebitRate" :clearable="false" placeholder-style="font-size:24rpx" />
                </u-form-item> -->
              </section>
            </template>
          </template>
        </u-form>
      </section>
    </main>

    <footer class="custom-button">
      <button @click="submit">提 交</button>
    </footer>

    <!-- 修改政策名称 -->
    <u-modal
      ref="uModal"
      v-model="showEditName"
      show-cancel-button
      title="修改政策名称"
      @confirm="submitEditName"
      async-close
    >
      <u-form :model="editName" ref="editNameForm" label-width="140">
        <section v-if="showEditName" style="padding: 0 30rpx">
          <u-form-item label="政策名称" prop="modelName" :border-bottom="false">
            <u-input
              v-model="editName.modelName"
              placeholder="请输入政策名称"
            />
          </u-form-item>
        </section>
      </u-form>
    </u-modal>
  </div>
</template>

<script>
  import {
    editSettmentPolicy,
    editSettmentPolicyName,
    detailSettmentPolicy,
    editSettmentMultPolicy,
    findSubChannelList
  } from '../../http/api';
  import { dateFormat } from '../../static/utils/date';
  import { mapState } from 'vuex';

  const pattern = /^0\.\d{0,3}$/;
  const costs = [
    'creditCost',
    'debitCost',
    'scanCost',
    'passCost',
    'creditSecTransCost',
  ];
  const rules = {
    debitCapValue: [{ required: true, message: '必填' }]
  };

  costs.forEach(
    (c) => (rules[c] = [{ pattern, message: '大于0、小于1且不得超过3位小数' }])
  );

  const nameRules = {
    modelName: [{ required: true, message: '必填' }]
  };
  rules.creditDiscountCost = [
    {
      validator: function (rule, val) {
        if (!val && val !== 0) return true;
        return pattern.test(val);
      },
      message: '大于0、小于1且不得超过3位小数'
    }
  ];
  rules.alipayLargerRate = [
    {
      validator: function (rule, val) {
        if (!val && val !== 0) return true;
        return pattern.test(val);
      },
      message: '大于0、小于1且不得超过3位小数'
    }
  ];
  function getDefaultCost() {
    return {
      creditCost: '0.000',
      debitCost: '0.000',
      debitCapValue: '0',
      scanCost: '0.000',
      passCost: '0.000',
      creditDiscountCost: '',
      creditSecTransCost: '0.000',
      alipayLargerRate: '',
      // eposCreditRate: '0.000', // 无卡费率(贷记)
      // eposDebitRate: '0.000' // 无卡费率(借记)
    };
  }
  export default {
    name: 'ChangeSettlementPolicy',
    filters: {
      dateFormat
    },
    data() {
      return {
        allInfo: {
          creditDiscountOpenConf: false,
          secTransOpenConf: false
        },
        form: {
          agentCode: '', // 编辑代理编号
          rateType: '', // 政策类型
          payOrgCode: '',
          cost: getDefaultCost(),
          costList: []
        },
        showEditName: false,
        subChannelList: [],
        currentSubChannel: 0,
        editName: {
          agentCode: '', // 编辑代理编, 必填
          rateType: '', // 政策类型, 必填
          modelName: '' // 新政策名称, 必填
        }
      };
    },
    computed: {
      ...mapState(['userInfo'])
    },
    async onLoad() {
      let dData = decodeURIComponent(this.$Route.query.info);
      let newData = JSON.parse(dData);
      this.allInfo = newData;

      const { data } = await detailSettmentPolicy({
        agentCode: this.allInfo.agentCode,
        rateType: this.allInfo.rateType
      });
      this.form.payOrgCode = data.settleRateBaseVo.payOrgCode;
      const rateList = data.rateInfPoList || [];

      if (this.allInfo.subChannelStatus === 1) {
        if (this.allInfo.type == 0) {
          const res = await findSubChannelList({
            payOrgCode: this.form.payOrgCode
          });

          const existedSubChannel = rateList.map((i) => i.subChannelCode);
          const allSubChannels = res.data || [];
          const addedSubChannel = allSubChannels.filter(
            (i) => !existedSubChannel.includes(i.subChannelCode)
          );

          addedSubChannel.forEach((i) => {
            rateList.push(Object.assign(i, getDefaultCost()));
          });
        }

        this.subChannelList = rateList.map((i) => {
          return { name: i.subChannelName };
        });

        this.form.costList = rateList;
      } else {
        this.form.cost = rateList[0] || getDefaultCost();
      }
    },
    onReady() {
      this.$refs.uForm.setRules(rules);
    },
    methods: {
      changeSubChannel(index) {
        this.currentSubChannel = index;
      },
      handleEditName() {
        this.editName.modelName = this.allInfo.modelName;
        this.showEditName = true;
        this.$nextTick(() => {
          this.$refs.editNameForm.setRules(nameRules);
          this.$refs.editNameForm.resetFields();
        });
      },
      submitEditName() {
        this.$refs.uModal.clearLoading();
        this.$refs.editNameForm.validate(async (valid) => {
          if (valid) {
            const { agentCode, rateType } = this.allInfo;
            this.editName.agentCode = agentCode;
            this.editName.rateType = rateType;

            const { code } = await editSettmentPolicyName(this.editName);
            if (code == '00') {
              this.allInfo.modelName = this.editName.modelName;
              this.$u.toast('名称修改成功');
            }
            this.showEditName = false;
          }
        });
      },
      submit() {
        this.$refs.uForm.validate((valid) => {
          if (valid) {
            if (!this.allInfo.subChannelStatus) {
              this.form.cost.creditDiscountCost =
                this.form.cost.creditDiscountCost === ''
                  ? null
                  : this.form.cost.creditDiscountCost;
            }

            const { agentCode, rateType } = this.allInfo;
            const { costList, cost } = this.form;

            var params = { agentCode, rateType },
              submitMethod;

            if (this.allInfo.subChannelStatus) {
              submitMethod = editSettmentMultPolicy;
              params.settlRateDTOList = costList;
            } else {
              submitMethod = editSettmentPolicy;
              params = { ...params, ...cost };
            }
            submitMethod(params).then((res) => {
              if (res.code == '00') {
                uni.showToast({
                  title: '修改成功',
                  icon: 'none'
                });
                setTimeout(() => {
                  this.$Router.back(1);
                }, 1500);
              }
            });
          } else {
            this.$u.toast('信息填写有误, 请检查！');
          }
        });
      }
    }
  };
</script>

<style lang="less" scoped>
  #changeSettlementPolicy {
    main {
      background-color: #fff;
      > header {
        padding: 10px;
        background-color: #fff;
        > div {
          display: flex;
          > p {
            flex: 1;
          }
          > span {
            margin-left: 5px;
            flex-shrink: 0;
            color: #004ea9;
          }
        }
        p {
          color: #8799a3;
          &:not(:last-of-type) {
            margin-bottom: 10rpx;
          }
          > span {
            margin-left: 10rpx;
            color: #333;
          }
        }
      }
      > div {
        padding: 20rpx;
        background-color: #f3f5f7;
        > h4 {
          margin: 0;
          color: #222222;
          font-weight: 500;
          font-size: 24rpx;
          span {
            font-size: 24rpx;
            font-weight: 400;
            color: #222222;
            opacity: 0.8;
          }
        }
      }
      > section {
        /deep/ .u-form {
          .u-form-item {
            padding: 20rpx 30rpx;
          }
          .u-form-item--left {
            flex: 1 !important;
          }
          .u-form-item--right {
            flex: none;
            width: 140rpx;
            border-radius: 6rpx;
            background: #eaeef1;
            input {
              text-align: center;
              color: #4e77d9;
            }
          }
          .u-form-item__message {
            text-align: right;
          }
        }
      }
    }
    .custom-button {
      margin-top: 40rpx;
    }
  }
</style>
