<template>
  <div id="openLower">
    <main>
      <section>
        <u-form :model="form" ref="uForm" class="u-form">
          <p class="interval" />
          <u-form-item label="选择代理商" label-width="160">
            <u-input
              type="select"
              :select-open="showAgentList"
              :value="`已选中${selectCount}个代理商`"
              @click="openLower"
            />
          </u-form-item>
          <p class="interval" />
          <section v-if="form.subChannelStatus === 0">
            <u-form-item label="借记卡(%)" prop="debitCost" :target="form.cost">
              <u-input
                type="digit"
                v-model="form.cost.debitCost"
                :clearable="false"
                placeholder-style="font-size:24rpx"
              />
            </u-form-item>
            <u-form-item
              label="借记卡封顶值(元)"
              prop="debitCapValue"
              :target="form.cost"
            >
              <u-input
                type="number"
                v-model="form.cost.debitCapValue"
                :clearable="false"
                placeholder-style="font-size:24rpx"
              />
            </u-form-item>
            <p class="interval" />
            <u-form-item
              label="信用卡(%)"
              prop="creditCost"
              :target="form.cost"
            >
              <u-input
                type="digit"
                v-model="form.cost.creditCost"
                :clearable="false"
                placeholder-style="font-size:24rpx"
              />
            </u-form-item>
            <u-form-item
              v-if="userInfo.showCreditDiscountOpenConf"
              label="信用卡特惠(%)"
              prop="creditDiscountCost"
              :target="form.cost"
            >
              <u-input
                type="digit"
                v-model="form.cost.creditDiscountCost"
                :clearable="false"
                placeholder="选填"
                placeholder-style="font-size:24rpx"
              />
            </u-form-item>
            <u-form-item
              v-if="userInfo.showSecTransOpenConf"
              label="信用卡秒到交易(%)"
              prop="creditSecTransCost"
              :target="form.cost"
            >
              <u-input
                type="digit"
                v-model="form.cost.creditSecTransCost"
                :clearable="false"
                placeholder-style="font-size:24rpx"
              />
            </u-form-item>
            <p class="interval" />
            <u-form-item label="扫码(%)" prop="scanCost" :target="form.cost">
              <u-input
                type="digit"
                v-model="form.cost.scanCost"
                :clearable="false"
                placeholder-style="font-size:24rpx"
              />
            </u-form-item>
            <u-form-item label="闪付(%)" prop="passCost" :target="form.cost">
              <u-input
                type="digit"
                v-model="form.cost.passCost"
                :clearable="false"
                placeholder-style="font-size:24rpx"
              />
            </u-form-item>
            <u-form-item
              label="支付宝大额(%)"
              prop="alipayLargerRate"
              :target="form.cost"
            >
              <u-input
                type="digit"
                v-model="form.cost.alipayLargerRate"
                :clearable="false"
                placeholder="选填"
                placeholder-style="font-size:24rpx"
              />
            </u-form-item>
          </section>

          <template v-if="form.subChannelStatus === 1">
            <template v-if="subChannelList.length">
              <u-tabs
                :list="subChannelList"
                :current="currentSubChannel"
                @change="changeSubChannel"
              ></u-tabs>
              <section
                v-for="(item, key) in form.costList"
                :key="key"
                v-show="currentSubChannel === key"
              >
                <u-form-item label="借记卡(%)" prop="debitCost" :target="item">
                  <u-input
                    type="digit"
                    v-model="item.debitCost"
                    :clearable="false"
                    placeholder-style="font-size:24rpx"
                  />
                </u-form-item>
                <u-form-item
                  label="借记卡封顶值(元)"
                  prop="debitCapValue"
                  :target="item"
                >
                  <u-input
                    type="number"
                    v-model="item.debitCapValue"
                    :clearable="false"
                    placeholder-style="font-size:24rpx"
                  />
                </u-form-item>
                <p class="interval" />
                <u-form-item label="信用卡(%)" prop="creditCost" :target="item">
                  <u-input
                    type="digit"
                    v-model="item.creditCost"
                    :clearable="false"
                    placeholder-style="font-size:24rpx"
                  />
                </u-form-item>
                <p class="interval" />
                <u-form-item label="扫码(%)" prop="scanCost" :target="item">
                  <u-input
                    type="digit"
                    v-model="item.scanCost"
                    :clearable="false"
                    placeholder-style="font-size:24rpx"
                  />
                </u-form-item>
                <u-form-item label="闪付(%)" prop="passCost" :target="item">
                  <u-input
                    type="digit"
                    v-model="item.passCost"
                    :clearable="false"
                    placeholder-style="font-size:24rpx"
                  />
                </u-form-item>
                <u-form-item
                  label="支付宝大额(%)"
                  prop="alipayLargerRate"
                  :target="item"
                >
                  <u-input
                    type="digit"
                    v-model="item.alipayLargerRate"
                    :clearable="false"
                    placeholder="选填"
                    placeholder-style="font-size:24rpx"
                  />
                </u-form-item>
                <!-- <p class="interval" />
                <u-form-item label="无卡费率(贷记)(%)" prop="eposCreditRate" :target="item">
                  <u-input type="digit" v-model="item.eposCreditRate" :clearable="false" placeholder-style="font-size:24rpx" />
                </u-form-item>
                <u-form-item label="无卡费率(借记)(%)" prop="eposDebitRate" :target="item">
                  <u-input type="digit" v-model="item.eposDebitRate" :clearable="false" placeholder-style="font-size:24rpx" />
                </u-form-item> -->
              </section>
            </template>
          </template>
        </u-form>
      </section>

      <!-- select teminals proup -->
      <u-popup mode="bottom" v-model="showAgentList">
        <div class="u-picker__toolbar">
          <text class="picker__cancel" @click="showAgentList = false"
            >取消</text
          >
          <text class="picker__confirm" @click="showAgentList = false"
            >确认</text
          >
        </div>
        <div class="filter-bysn">
          <u-field
            v-model="agentName"
            label="代理名称"
            placeholder="代理名称(支持模糊查询)"
          >
            <template #right>
              <u-button size="mini" type="primary" @click="openLower"
                >搜索</u-button
              >
            </template>
          </u-field>
        </div>
        <p class="tips">已开通代理商不可选中</p>
        <scroll-view style="height: 36vh" scroll-y="true">
          <checkbox-group @change="checkboxGroupChange">
            <u-cell-group :border="false">
              <label v-for="(t, index) in dirAgentList" :key="index">
                <u-cell-item
                  :border-bottom="false"
                  border-top
                  :title="t.agentCode + '-' + t.agentName"
                  :arrow="false"
                >
                  <checkbox
                    :disabled="t.openStat == 1"
                    style="transform: scale(0.8)"
                    slot="right-icon"
                    :value="t.agentCode"
                    :checked="
                      form.agentCodeList.length &&
                      form.agentCodeList.indexOf(t.agentCode) != -1
                    "
                  />
                </u-cell-item>
              </label>
            </u-cell-group>
          </checkbox-group>
        </scroll-view>
      </u-popup>
    </main>

    <footer class="custom-button">
      <button @click="submit">提 交</button>
    </footer>
  </div>
</template>

<script>
  import {
    openLowerSettmentPolicy,
    getOpenLowerList,
    openMultLowerSettmentPolicy,
    detailSettmentPolicy
  } from '../../http/api';
  import { mapState } from 'vuex';

  const pattern = /^0\.\d{0,3}$/;
  const costs = [
    'creditCost',
    'debitCost',
    'scanCost',
    'passCost',
    'creditSecTransCost',
  ];
  const rules = {
    debitCapValue: [{ required: true, message: '必填' }]
  };
  costs.forEach(
    (c) => (rules[c] = [{ pattern, message: '大于0、小于1且不得超过3位小数' }])
  );
  rules.creditDiscountCost = [
    {
      validator: function (rule, val) {
        if (!val && val !== 0) return true;
        return pattern.test(val);
      },
      message: '大于0、小于1且不得超过3位小数'
    }
  ];
  rules.alipayLargerRate = [
    {
      validator: function (rule, val) {
        if (!val && val !== 0) return true;
        return pattern.test(val);
      },
      message: '大于0、小于1且不得超过3位小数'
    }
  ];

  function getDefaultCost() {
    return {
      creditCost: '0.000',
      debitCost: '0.000',
      debitCapValue: '0',
      scanCost: '0.000',
      passCost: '0.000',
      creditDiscountCost: '',
      alipayLargerRate: '',
      creditSecTransCost: '0.000',
      // eposCreditRate: '0.000', // 无卡费率(贷记)
      // eposDebitRate: '0.000' // 无卡费率(借记)
    };
  }
  export default {
    name: 'OpenLower',
    data() {
      return {
        form: {
          agentCodeList: [],
          rateType: '',
          cost: getDefaultCost(),
          costList: [],
          subChannelStatus: ''
        },
        showAgentList: false,
        dirAgentList: [],
        subChannelList: [],
        currentSubChannel: 0,
        agentName: ''
      };
    },
    computed: {
      selectCount() {
        return this.form.agentCodeList.length;
      },
      ...mapState(['userInfo'])
    },
    async onLoad() {
      let dData = decodeURIComponent(this.$Route.query.info);
      let newData = JSON.parse(dData);
      this.form = Object.assign(this.form, newData);

      const { agentCode, rateType } = this.form;

      const { data } = await detailSettmentPolicy({ agentCode, rateType });
      const rateList = data.rateInfPoList || [];

      if (this.form.subChannelStatus === 1) {
        this.subChannelList = rateList.map((i) => {
          return { name: i.subChannelName };
        });
        this.form.costList = rateList;
      } else {
        this.form.cost = rateList[0] || getDefaultCost();
      }
    },
    onReady() {
      this.$refs.uForm.setRules(rules);
    },
    methods: {
      changeSubChannel(index) {
        this.currentSubChannel = index;
      },
      openLower() {
        getOpenLowerList({
          rateType: this.form.rateType,
          agentName: this.agentName,
          subChannelStatus: this.form.subChannelStatus
        }).then((res) => {
          if (res.code == '00') {
            this.dirAgentList = res.data || [];
            this.showAgentList = true;
          }
        });
      },
      checkboxGroupChange(e) {
        this.form.agentCodeList = e.detail.value;
      },
      submit() {
        if (!this.form.agentCodeList.length) {
          return uni.showToast({
            title: '未选择代理商!',
            icon: 'none'
          });
        }
        this.$refs.uForm.validate((valid) => {
          if (valid) {
            if (!this.form.subChannelStatus) {
              this.form.cost.creditDiscountCost =
                this.form.cost.creditDiscountCost === ''
                  ? null
                  : this.form.cost.creditDiscountCost;
            }

            const { costList, cost, rateType, agentCodeList } = this.form;

            var params = { agentCodeList, rateType },
              submitMethod;

            if (this.form.subChannelStatus) {
              submitMethod = openMultLowerSettmentPolicy;
              params.settlRateDTOList = costList;
            } else {
              submitMethod = openLowerSettmentPolicy;
              params = { ...params, ...cost };
            }
            submitMethod(params).then((res) => {
              if (res.code == '00') {
                uni.showToast({
                  title: '开通成功',
                  icon: 'none'
                });
                setTimeout(() => {
                  this.$Router.back(1);
                }, 1500);
              }
            });
          } else {
            this.$u.toast('信息填写有误, 请检查！');
          }
        });
      }
    }
  };
</script>

<style lang="less" scoped>
  #openLower {
    main {
      background-color: #fff;
      > div {
        padding: 20rpx;
        background-color: #f3f5f7;
        > h4 {
          margin: 0;
          color: #222222;
          font-weight: 500;
          font-size: 24rpx;
          span {
            font-size: 24rpx;
            font-weight: 400;
            color: #222222;
            opacity: 0.8;
          }
        }
      }
      > section {
        /deep/ .u-form {
          .u-form-item {
            padding: 20rpx 30rpx;
          }
          > section {
            .u-form-item--left {
              flex: 1 !important;
            }
            .u-form-item--right {
              flex: none;
              width: 140rpx;
              border-radius: 6rpx;
              background: #eaeef1;
              input {
                text-align: center;
                color: #4e77d9;
              }
            }
            .u-form-item__message {
              text-align: right;
            }
          }
        }
      }
    }
    .custom-button {
      margin-top: 40rpx;
    }
    .u-picker__toolbar {
      display: flex;
      justify-content: space-between;
      padding: 26rpx 32rpx;
      border-bottom: 1px soild #e5e5e5;
      .picker__confirm {
        color: #576b95;
      }
    }
    /deep/ checkbox .wx-checkbox-input {
      border-radius: 50% !important;
    }

    /deep/ checkbox .wx-checkbox-input.wx-checkbox-input-checked {
      color: #004ea9;
    }
    .tips {
      padding: 3vw;
      color: darkgray;
      font-size: 24rpx;
      span {
        color: #333;
      }
    }
  }
</style>
