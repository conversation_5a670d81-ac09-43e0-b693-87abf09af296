<template>
  <div id="policyList">
    <main>
      <view class="filter-header">
        <u-dropdown ref="uDropdown" :close-on-click-mask="false" :close-on-click-self="false" @open="openFilterHeader">
          <u-dropdown-item title="筛选">
            <view class="filter-form">
              <u-field v-if="params.dataType == 1" v-model="params.agentName" label="代理商名称" placeholder="代理商名称 (支持模糊查询)" />
              <u-field
                disabled
                label="政策类型"
                placeholder="请选择政策类型"
                right-icon="arrow-down-fill"
                :value="modelTypeVal"
                @click="selectModelType"
              />
              <div class="btnTools">
                <u-button size="medium" @click="toReset">重置</u-button>
                <u-button color="#004ea9" type="primary" size="medium" @click="$refs.uDropdown.close(), getPageData(true)">确定</u-button>
              </div>
            </view>
          </u-dropdown-item>
        </u-dropdown>
        <view v-if="agentLevel == 1" class="add-model" @click="addPolicy">新增政策</view>
      </view>
      <u-sticky>
        <option-tab opts="我的政策,服务商政策" :current="currentTab" @select="select" />
      </u-sticky>
      <no-content v-if="show" />

      <section class="list-data" v-show="!show">
        <div class="model-item" v-for="(m, index) in modelList" :key="index">
          <p>
            政策名称: <span style="font-weight: bold">{{ m.modelName }}</span>
          </p>
          <p v-if="params.dataType == 1">
            代理名称: <span>{{ m.agentName }}</span>
          </p>
          <p v-if="params.dataType == 1">
            代理编号: <span>{{ m.agentCode }}</span>
          </p>
          <p>
            创建时间: <span>{{ dateFormat(m.createTime).substring(0, 10) }}</span>
          </p>
          <section v-if="m.subChannelStatus === 1">
            <u-tag text="聚合" type="error" size="mini"/>
          </section>
          <div class="tools-btn u-border-top">
            <span v-if="params.dataType == '0'" @click="openLower(m)">开通服务商</span>
            <span v-if="agentLevel == 1 && params.dataType == 1" class="close-policy" @click="closePolicy(m.agentCode, m.rateType)"
              >关闭政策</span
            >
            <span v-if="params.dataType == 1 || agentLevel == 1" @click="editPolicy(m)">编辑{{ params.dataType == 0 ? '/详情' : '' }}</span>
          </div>
        </div>
      </section>
      <u-loadmore v-if="!show" :status="status" @loadmore="getPageData(false)" />
    </main>
    <u-select v-model="showModelType" mode="single-column" :list="modelType" @confirm="modelTypeConfirm" />
  </div>
</template>

<script>
import { getSettmentPolicyList, closeLowerSettmentPolicy, getAgentRateType } from '../../http/api';
import { dateFormat } from '../../static/utils/date';
import OptionTab from '../../components/OptionTab.vue';

export default {
  name: 'PolicyList',
  components: {
    OptionTab
  },
  data() {
    return {
      total: null,
      show: false,
      modelList: [],
      dirAgentList: [],
      modelTypeVal: '',
      modelType: [],
      showModelType: false,
      oldParams: null,
      params: {
        pageNo: 1,
        pageSize: 10,
        agentName: '',
        rateType: '', // 政策类型
        dataType: '0' // 0本级/1直属/2全部下级(仅限一代)
      },
      currentTab: 0,
      status: 'loading'
    };
  },
  computed: {
    agentLevel() {
      return this.$store.state.userInfo.agentLevel;
    }
  },
  onLoad() {},
  onShow() {
    this.oldParams = null;
    this.getPageData(true);
  },
  onReachBottom() {
    this.getPageData(false);
  },
  methods: {
    dateFormat,
    select(data) {
      if (this.currentTab == data) return;
      if (!this.currentTab) {
        this.params.agentName = '';
      }
      this.params.dataType = this.currentTab = data;
      this.oldParams = null;
      this.getPageData(true);
    },
    modelTypeConfirm(val) {
      const { label, value } = val[0];
      this.modelTypeVal = label;
      this.params.rateType = value;
      this.showModelType = false;
    },
    selectModelType() {
      getAgentRateType().then(res => {
        if (res.code == '00') {
          this.modelType = [];
          res.data.forEach(i => {
            this.modelType.push({ label: i.modelName, value: i.rateType });
          });
          this.showModelType = true;
        }
      });
    },
    openFilterHeader() {
      this.oldParams = JSON.parse(JSON.stringify(this.params));
    },
    toReset() {
      this.params.agentName = this.modelTypeVal = this.params.rateType = '';
    },
    addPolicy() {
      this.$Router.push({ name: 'SettmentAddPolicy' });
    },
    editPolicy(info) {
      info.type = this.params.dataType;
      this.$Router.push({ name: 'SettmentEditPolicy', params: { info: encodeURIComponent(JSON.stringify(info)) } });
    },
    openLower(info) {
      this.$Router.push({ name: 'SettmentOpenLower', params: { info: encodeURIComponent(JSON.stringify(info)) } });
    },
    closePolicy(agentCode, rateType) {
      uni.showModal({
        content: '此功能关闭该二级代理及其所有服务商，请确认！',
        success: res => {
          if (res.confirm) {
            closeLowerSettmentPolicy({ agentCode, rateType }).then(res => {
              if (res.code == '00') {
                uni.showToast({
                  title: '政策已关闭',
                  icon: 'none'
                });
                this.oldParams = null;
                this.getPageData(true);
              }
            });
          }
        }
      });
    },
    getPageData(isInquire) {
      if (!isInquire && this.status == 'nomore') return;
      if (isInquire && JSON.stringify(this.oldParams) == JSON.stringify(this.params)) {
        return;
      }
      this.status = 'loading';
      this.params.pageNo = isInquire ? 1 : this.params.pageNo + 1;
      getSettmentPolicyList(this.params).then(res => {
        if (res.code == '00') {
          if (res.data.list && res.data.list.length != 0) {
            this.show = false;
            this.total = res.data.total;

            !isInquire
              ? res.data.list.forEach(i => {
                  this.modelList.push(i);
                })
              : (this.modelList = res.data.list);
            isInquire &&
              uni.pageScrollTo({
                scrollTop: 0
              });
          } else {
            this.show = true;
          }
          if (this.modelList.length >= this.total) {
            // 数据全部加载完成
            this.status = 'nomore';
          } else {
            this.status = 'loadmore';
          }
        }
      });
    }
  }
};
</script>

<style lang="less">
@import '../../static/css/game.less';

#policyList {
  height: 100%;
  main {
    .filter-header {
      position: relative;
      .add-model {
        position: absolute;
        right: 40rpx;
        top: 50%;
        z-index: 12;
        transform: translateY(-50%);
        color: #004ea9;
      }
    }
    .list-data {
      padding: 20rpx 30rpx 0;
      .model-item {
        background: #fff;
        border-radius: 20rpx;
        padding: 20rpx 30rpx;
        margin-bottom: 20rpx;
        > p {
          margin: 0 0 10rpx;
          color: #8799a3;
          > span {
            margin-left: 10rpx;
            color: #333;
          }
        }
        > span {
          font-size: 24rpx;
          color: #999;
        }
        .tools-btn {
          display: flex;
          padding-top: 20rpx;
          margin-top: 10rpx;
          color: #004ea9;
          > span {
            flex: 1;
            text-align: center;
            border-right: 1px solid #e5e5e5;
            &:last-of-type {
              border: none;
            }
          }
          .close-policy {
            color: #e54d42;
          }
        }
      }
    }
  }
  .u-picker__toolbar {
    display: flex;
    justify-content: space-between;
    padding: 26rpx 32rpx;
    border-bottom: 1px soild #e5e5e5;
    .picker__confirm {
      color: #576b95;
    }
  }
  /deep/ checkbox .wx-checkbox-input {
    border-radius: 50% !important;
  }

  /deep/ checkbox .wx-checkbox-input.wx-checkbox-input-checked {
    color: #004ea9;
  }
  .tips {
    padding: 3vw;
    color: darkgray;
    font-size: 24rpx;
    span {
      color: #333;
    }
  }

  form {
    button {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 50%;
      z-index: 1;
    }
  }

  .u-form {
    section {
      padding: 0 30rpx;
    }
  }
  /deep/ .u-cell {
    padding: 20rpx 30rpx;
  }
}
</style>
