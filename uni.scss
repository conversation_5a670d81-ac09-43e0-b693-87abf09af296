
@import 'uview-ui/theme.scss';

/* ==================== 现代化设计系统 ==================== */

/* 主色调 */
$primary-color: #1A73E8;
$primary-light: #4285F4;
$primary-dark: #1557B0;
$secondary-color: #34A853;
$warning-color: #FBBC04;
$error-color: #EA4335;

/* 中性色 */
$text-primary: #202124;
$text-secondary: #5F6368;
$text-tertiary: #9AA0A6;
$text-disabled: #BDC1C6;

/* 背景色 */
$bg-primary: #FFFFFF;
$bg-secondary: #F8F9FA;
$bg-tertiary: #F1F3F4;
$bg-overlay: rgba(32, 33, 36, 0.6);

/* 边框色 */
$border-light: #E8EAED;
$border-medium: #DADCE0;
$border-dark: #5F6368;

/* 阴影 */
$shadow-light: 0 1px 3px rgba(60, 64, 67, 0.08);
$shadow-medium: 0 2px 8px rgba(60, 64, 67, 0.15);
$shadow-heavy: 0 4px 16px rgba(60, 64, 67, 0.2);

/* 圆角 */
$radius-small: 8rpx;
$radius-medium: 16rpx;
$radius-large: 24rpx;
$radius-xl: 32rpx;

/* 间距系统 */
$spacing-xs: 8rpx;
$spacing-sm: 16rpx;
$spacing-md: 24rpx;
$spacing-lg: 32rpx;
$spacing-xl: 48rpx;
$spacing-xxl: 64rpx;

/* 字体大小 */
$font-size-xs: 20rpx;
$font-size-sm: 24rpx;
$font-size-base: 28rpx;
$font-size-lg: 32rpx;
$font-size-xl: 36rpx;
$font-size-xxl: 48rpx;
$font-size-title: 56rpx;

/* 字体权重 */
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

/* 行高 */
$line-height-tight: 1.2;
$line-height-normal: 1.4;
$line-height-relaxed: 1.6;

/* 渐变 */
$gradient-primary: linear-gradient(135deg, #1A73E8 0%, #4285F4 100%);
$gradient-secondary: linear-gradient(135deg, #34A853 0%, #46C063 100%);
$gradient-card: linear-gradient(135deg, #FFFFFF 0%, #F8F9FA 100%);

/* 兼容旧版本变量 */
$uni-bg-color: $bg-primary;
$theme-color: $primary-color;
$uni-border-color: $border-light;

/* 引入响应式样式 */
@import 'static/css/responsive.scss';
