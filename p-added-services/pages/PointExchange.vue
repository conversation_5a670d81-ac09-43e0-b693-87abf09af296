<template>
    <div id="pointExchange">
        <header>
            <p>提示: 保存二维码图片在支付宝中扫一扫识别</p>
            <u-tag text="使用场景" shape="circleLeft" @click="showSwiper = true" />
        </header>

        <section>
            <div @click="openQrMask(l.url,l.logoIcon)" v-for="(l,index) in list" :key="index">
                <image :src="require(`../static/images//point${index+1}.jpeg`)" mode="widthFix" />
                <div>
                    <u-tag type="success" :text="l.name" mode="dark" />
                </div>
            </div>
        </section>

        <u-mask :show="showSwiper" @click="showSwiper = false">
            <view class="warp">
                <swiper class="material-swiper">
                    <swiper-item v-for="(s, index) in 3" :key="index">
                        <image :src="require(`../static/images/pointHelp${index + 1}.jpeg`)" mode="widthFix">
                    </swiper-item>
                </swiper>
            </view>
        </u-mask>

        <view v-if="showQRcard" class="qrcard-proup">
            <u-popup v-model="showQRcard" mode="center" :zoom="false" border-radius="14" height="auto">
                <view class="content">
                    <div class="game-qrcard">
                        <create-qrcode v-show="!imageUrl" ref="qrcode" :val="qrUrl" :size="340" :showLoading="false" @result="resultQr" :icon="logoIcon" :iconSize="20" />
                        <image v-show="imageUrl" :src="imageUrl" mode="widthFix" show-menu-by-longpress />
                        <div>长按保存二维码在支付宝中扫一扫识别</div>
                    </div>
                    <div class="close">
                        <u-icon name="close-circle" size="54" color="#e5e5e5" @click="showQRcard = false" />
                    </div>
                </view>
            </u-popup>
        </view>
    </div>
</template>

<script>
import CreateQrcode from "tki-qrcode"

export default {
    name: 'PointExchange',
    components: { CreateQrcode },
    data() {
        return {
            showQRcard: false,
            showSwiper: false,
            qrUrl: '',
            imageUrl: '',
            logoIcon: '',
            list: [
                {
                    name: '中行积分兑换',
                    url: 'alipays://platformapi/startapp?appId=****************&page=changyou_business/changyou_bank_of_china/cbIndex/cbIndex?mediaId=mt2000000046',
                    logoIcon: '/p-added-services/static/images/pointType1.png'
                },
                {
                    name: '移动积分兑换',
                    url: 'alipays://platformapi/startapp?appId=****************&page=changyou_business/changyou_china_mobile/index/index?mediaId=mt2000000046',
                    logoIcon: '/p-added-services/static/images/pointType2.png'
                }
            ]
        };
    },
    onLoad() {
    },
    methods: {
        resultQr(val) {
            this.imageUrl = val
        },
        openQrMask(url, logoIcon) {
            this.imageUrl = ''
            this.qrUrl = url
            this.logoIcon = logoIcon
            this.showQRcard = true
            this.$nextTick(() => {
                this.$refs.qrcode._makeCode()
            })
        },
    }
};
</script>

<style lang="less" scoped>
#pointExchange {
    height: 100%;
    background-color: #f3f5f7;
    > header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        > p {
            padding: 30rpx;
            color: #ee934e;
            font-size: 26rpx;
        }
    }

    > section {
        display: flex;
        justify-content: space-between;
        padding: 30rpx;
        padding-top: 0;
        > div {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            width: calc((100% - 30rpx) / 2);
            background-color: #fff;
            border-radius: 20rpx;
            font-size: 0;
            > image {
                width: 100%;
                border-radius: 20rpx 20rpx 0 0;
            }
            > div {
                padding: 20rpx;
                padding-top: 0;
                font-size: 28rpx;
                text-align: center;
            }
        }
    }
    .qrcard-proup {
        /deep/ .u-mode-center-box {
            border-radius: 0 !important;
            background-color: transparent;
        }
        .content {
            position: relative;
            .game-qrcard {
                padding: 20rpx;
                background-color: #fff;
                border-radius: 10rpx;
                width: 380rpx;
                > image {
                    width: 340rpx;
                    height: 340rpx;
                }
                > div {
                    margin-top: 6rpx;
                    text-align: center;
                    font-size: 24rpx;
                    color: #999;
                }
            }
            .close {
                padding-top: 30rpx;
                text-align: center;
            }
        }
    }
    .warp {
        height: 100%;
    }
    /deep/ .material-swiper {
        height: 100%;
        swiper-item {
            display: flex;
            align-items: center;
            justify-content: center;
            image {
                width: 74%;
                border-radius: 20rpx;
            }
        }
    }
}
</style>
