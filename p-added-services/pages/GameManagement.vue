<template>
    <div id="gameManagement">
        <u-cell-group :border="false">
            <u-cell-item title="游戏列表" @click="toLink('GameList')" />
            <u-cell-item title="玩家充值记录" @click="toLink('RechargeList')" />
            <u-cell-item title="玩家推广记录" @click="toLink('RegisterList')" />
            <u-cell-item title="代理商结算记录" @click="toLink('SettlementList')" />
        </u-cell-group>
    </div>
</template>

<script>
export default {
    name: "GameManagement",
    methods: {
        toLink(name) {
            this.$Router.push({ name })
        }
    },
};
</script>

<style lang="less" scoped>
#gameManagement {
    padding-top: 20rpx;
}
</style>