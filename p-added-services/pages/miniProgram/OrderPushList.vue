<template>
    <div id="orderPushList">
        <header class="filter-header">
            <u-dropdown ref="uDropdown" :close-on-click-mask="false" :close-on-click-self="false" @open="openFilterHeader">
                <u-dropdown-item title="更多筛选条件">
                    <view class="filter-form">
                        <u-field v-model="ajaxParams.agentCode" label="代理商编号" placeholder="请输入代理商编号" />
                        <view class="radio-group">
                            <u-radio-group v-model="ajaxParams.productSeriesId">
                                <u-radio :name="null" shape="square">全部</u-radio>
                                <u-radio name="01" shape="square">白酒</u-radio>
                                <u-radio name="02" shape="square">三疯茶道</u-radio>
                            </u-radio-group>
                        </view>
                        <div class="btnTools">
                            <u-button size="medium" @click="toReset">重置</u-button>
                            <u-button size="medium" color="#004ea9" type="primary" @click="$refs.uDropdown.close(), getPageData()">确定</u-button>
                        </div>
                    </view>
                </u-dropdown-item>
            </u-dropdown>
            <view class="fix-right" @click="toSettlementRecord">
                <text>结算记录</text>
                <!-- <u-icon name="arrow-right" /> -->
            </view>
        </header>

        <u-sticky>
            <u-tabs :list="orderNavBar" :is-scroll="false" :current="active" @change="changeData" />
        </u-sticky>
        <main>
            <no-content v-if="show" />

            <view class="list-data" v-show="!show">
                <section v-for="(p, index) in pageData" :key="index">
                    <div>订单编号:{{p.orderNo}} <span>{{p.orderStatus | orderStatusFormat}}</span> </div>
                    <p><span>系列名称:</span> {{p.seriesName}}</p>
                    <p><span>商品总价:</span> ¥<strong>{{ p.productPrice}}</strong></p>
                    <p><span>运费:</span> ¥<strong>{{p.postage}}</strong></p>
                    <p><span>订单总价:</span> ¥<strong>{{p.totalPrice}}</strong></p>
                    <p><span>代理商编号:</span> {{p.agentCode}}</p>
                </section>
            </view>

            <u-loadmore v-if="!show" :status="status" @loadmore="loadmore" />

        </main>
    </div>
</template>



<script>
import { getOrderPushList } from "../../../http/api";
import { toDecimal2 } from '../../../static/utils/date';

const orderStatus = {
    0: '已生成',
    1: '待支付',
    2: '待发货',
    3: '支付失败',
    4: '已发货',
    5: '已收货',
    6: '售后状态',
    7: '部分商品售后状态',
    8: '状态未知',

}
const orderNavBar = [{ name: '全部' }, { name: '待支付' }, { name: '待发货' }, { name: '已发货' }, { name: '已收货' }]
export default {
    name: "OrderPushList",
    filters: {
        toDecimal2,
        orderStatusFormat(val) {
            return orderStatus[val]
        }
    },
    data() {
        return {
            active: 0,
            total: null,
            status: 'loading',
            show: false,
            isDisabled: false,
            pageData: [],
            orderStatus,
            orderNavBar,
            oldAjaxParams: null,
            ajaxParams: {
                pageNo: 1, //当前页
                pageSize: 10, //每页数量
                agentCode: "", //代理商编号
                orderStatus: "", //订单状态
                productSeriesId: null, //交易类型 01:白酒 02:茶道
            },
        };
    },
    onLoad() {
        this.getPageData();
    },
    onReachBottom() {
        this.loadmore();
    },
    methods: {
        changeData(index) {
            this.active = index

            if (this.active == 0) {
                this.ajaxParams.orderStatus = '';
            }
            else if (this.active > 2) {
                this.ajaxParams.orderStatus = this.active + 1;
            } else {
                this.ajaxParams.orderStatus = this.active;
            }
            this.getPageData(true);
        },
        // 重置参数
        toReset() {
            this.ajaxParams.pageNo = 1;
            this.ajaxParams.agentCode = '';
            this.ajaxParams.productSeriesId = null
        },
        toSettlementRecord() {
            this.$Router.push({ name: 'SettlementList' })
        },
        openFilterHeader() {
            this.oldAjaxParams = JSON.parse(JSON.stringify(this.ajaxParams))
        },
        getPageData(isNavBar = false) {
            if (!isNavBar && JSON.stringify(this.oldAjaxParams) == JSON.stringify(this.ajaxParams)) {
                return;
            }
            this.status = 'loading'
            this.ajaxParams.pageNo = 1;
            getOrderPushList(this.ajaxParams).then((res) => {
                if (res.code == "00") {
                    this.total = res.data.total;
                    if (res.data.list.length != 0) {
                        this.show = false;
                        this.pageData = res.data.list;
                        uni.pageScrollTo({
                            scrollTop: 0
                        });
                        if (this.pageData.length >= this.total) {
                            // 数据全部加载完成
                            this.status = 'nomore';
                        } else {
                            this.status = 'loadmore';
                        }
                    } else {
                        this.show = true;
                    }
                }
            });
        },
        loadmore() {
            if (this.status == 'nomore') return;
            this.status = 'loading'
            this.ajaxParams.pageNo = this.ajaxParams.pageNo + 1;
            getOrderPushList(this.ajaxParams).then((res) => {
                if (res.code == "00") {
                    this.total = res.data.total;
                    res.data.list.forEach((i) => {
                        this.pageData.push(i);
                    });
                    this.loading = false;
                    if (this.pageData.length >= this.total) {
                        // 数据全部加载完成
                        this.status = 'nomore';
                    } else {
                        this.status = 'loadmore';
                    }
                }
            });
        },
    },
};
</script>

<style lang="less" scoped>
@import "../../../static/css/game.less";

#orderPushList {
    > main {
        > .list-data {
            padding: 20rpx 30rpx 0;
            > section {
                padding: 20rpx 32rpx;
                margin-bottom: 20rpx;
                background-color: #fff;
                border-bottom: 2rpx solid #e5e5e5;
                border-radius: 10rpx;
                > div {
                    display: flex;
                    justify-content: space-between;
                    font-size: 24rpx;
                    border-bottom: 2rpx solid #f3f5f7;
                    padding-bottom: 20rpx;
                    color: #777;
                    > span {
                        flex-shrink: 0;
                        color: rgb(236, 66, 4);
                    }
                }
                > p {
                    display: flex;
                    margin: 0.5em 0;
                    &:last-of-type {
                        margin-bottom: 0;
                    }
                    > span {
                        flex-shrink: 0;
                        width: 24%;
                        color: #444;
                        font-size: 26rpx;
                    }
                    > strong {
                        font-weight: normal;
                        margin-left: 0.1em;
                    }
                }
            }
        }
    }
    .filter-header {
        position: relative;
        /deep/ .u-dropdown__menu__item {
            justify-content: flex-start;
        }
        .fix-right {
            position: absolute;
            right: 30rpx;
            top: 0;
            height: 100%;
            display: flex;
            align-items: center;
            color: #004ea9;
            z-index: 12;
        }
    }
}
</style>