<template>
  <div id="goodsList">
    <u-sticky>
      <header class="filter-header">
        <u-dropdown ref="uDropdown" :close-on-click-mask="false" :close-on-click-self="false" @open="openFilterHeader">
          <u-dropdown-item title="筛选">
            <view class="filter-form">
              <u-field v-model="ajaxParams.productName" label="商品名称" placeholder="请输入商品名称" />
              <div class="btnTools">
                <u-button size="medium" @click="toReset">重置</u-button>
                <u-button size="medium" color="#004ea9" type="primary" @click="$refs.uDropdown.close(), getPageData()">确定</u-button>
              </div>
            </view>
          </u-dropdown-item>
        </u-dropdown>
      </header>
    </u-sticky>

    <main>
      <no-content v-if="show" />

      <view class="list-data" v-show="!show">
        <section v-for="(p, index) in pageData" :key="index">
          <section>
            <div>
              <div class="title_pic">
                <image lazy-load :src="p.productUrl" mode="scaleToFill" />
              </div>
              <div class="good_info">
                <p>{{ p.productName }}</p>
                <p>
                  <text v-if="p.remark"> {{ p.remark }}</text>
                </p>
                <p>
                  <span :class="'level' + p.level" v-if="p.level && p.level != 4">
                    {{ p.level == 1 ? '沸' : p.level == 2 ? '热' : p.level == 3 ? '荐' : '' }}
                  </span>
                  <span>
                    {{ p.enterType == 0 ? '自营' : p.enterType == 1 ? '加盟' : '' }}
                  </span>
                </p>
                <p>
                  ￥{{ p.price }}
                  <span v-if="p.price != p.originPrice">￥{{ p.originPrice }}</span>
                </p>
              </div>
            </div>
            <div @click="openQrMask(p.productId)">去看看</div>
          </section>
          <div>
            <p v-if="agentLevel == 1">
              一代返佣比例: <span>{{ p.firstAgentRate * 100 }}%</span>
            </p>
            <p>
              推广返佣比例: <span>{{ p.lowerAgentRate * 100 }}%</span>
            </p>
          </div>
        </section>
      </view>

      <u-loadmore v-if="!show" :status="status" @loadmore="loadmore" />
    </main>

    <!-- 商品二维码 -->
    <u-popup v-model="showQrMask" mode="center" class="qrProup" closeable close-icon-size="36">
      <div class="content">
        <image :src="qrImgData" @longpress="openAppToolBox(qrImgData)" show-menu-by-longpress />
        <text>长按查看更多操作</text>
      </div>
    </u-popup>
  </div>
</template>

<script>
import { getGoodsList, getMiniProgramQrCode } from '../../../http/api'
import { base64ToPath } from 'image-tools'
const navBarTitleList = ['特色酒水', '三疯茶道', '东山岛海产品', '青田泽济农资', '鹏农商城']

export default {
  name: 'GoodsList',
  data() {
    return {
      navBarTitle: '',
      total: null,
      status: 'loading',
      show: false,
      showQrMask: false,
      qrImgData: '',
      isDisabled: false,
      pageData: [],
      oldAjaxParams: null,
      ajaxParams: {
        pageNo: 1, //当前页
        pageSize: 10, //每页数量
        productName: '',
        enterType: '', //入驻方式:0:自营;1:加盟
        appletType: '' //小程序类型 01:白酒;02:茶道
      }
    }
  },
  computed: {
    agentLevel() {
      return this.$store.state.userInfo.agentLevel
    }
  },
  onLoad() {
    const { appletType } = this.$Route.query
    this.ajaxParams.appletType = appletType
    this.navBarTitle = navBarTitleList[Number(appletType) - 1]
    this.getPageData()
  },
  onReady() {
    uni.setNavigationBarTitle({ title: this.navBarTitle })
  },
  onReachBottom() {
    this.loadmore()
  },
  methods: {
    // 重置参数
    toReset() {
      this.ajaxParams.pageNo = 1
      this.ajaxParams.productName = ''
    },
    openFilterHeader() {
      this.oldAjaxParams = JSON.parse(JSON.stringify(this.ajaxParams))
    },
    getPageData() {
      if (JSON.stringify(this.oldAjaxParams) == JSON.stringify(this.ajaxParams)) {
        return
      }
      this.ajaxParams.pageNo = 1
      this.status = 'loading'
      getGoodsList(this.ajaxParams).then(res => {
        if (res.code == '00') {
          this.total = res.data.total
          if (res.data.list.length != 0) {
            this.show = false
            this.pageData = res.data.list
            uni.pageScrollTo({
              scrollTop: 0
            })
            if (this.pageData.length >= this.total) {
              // 数据全部加载完成
              this.status = 'nomore'
            } else {
              this.status = 'loadmore'
            }
          } else {
            this.show = true
          }
        }
      })
    },
    loadmore() {
      if (this.status == 'nomore') return
      this.status = 'loading'
      this.ajaxParams.pageNo = this.ajaxParams.pageNo + 1
      getGoodsList(this.ajaxParams).then(res => {
        if (res.code == '00') {
          this.total = res.data.total
          res.data.list.forEach(i => {
            this.pageData.push(i)
          })
          if (this.pageData.length >= this.total) {
            // 数据全部加载完成
            this.status = 'nomore'
          } else {
            this.status = 'loadmore'
          }
        }
      })
    },
    openQrMask(id) {
      getMiniProgramQrCode(id).then(res => {
        if (res.code == '00') {
          base64ToPath(`data:image/png;base64,${res.data}`)
            .then(path => {
              this.qrImgData = path
              this.showQrMask = true
            })
            .catch(error => {
              console.error(error)
            })
        }
      })
    },
    openAppToolBox(imgPath) {
      // #ifdef APP-PLUS
      uni.showActionSheet({
        itemList: ['保存', '分享'],
        success: function ({ tapIndex }) {
          switch (tapIndex) {
            case 0:
              uni.saveImageToPhotosAlbum({
                filePath: imgPath,
                success: function () {
                  uni.showToast({ title: '图片保存成功' })
                }
              })
              break
            case 1:
              plus.share.sendWithSystem(
                { pictures: [imgPath] },
                function () {
                  console.log('分享成功')
                },
                function (e) {
                  console.log(e)
                }
              )
              break
            default:
              break
          }
        }
      })
      // #endif
    }
  }
}
</script>

<style lang="less" scoped>
@import '../../../static/css/game.less';

#goodsList {
  > main {
    .list-data {
      padding: 20rpx 30rpx 0;
      > section {
        margin-bottom: 20rpx;
        padding: 20rpx 24rpx;
        background-color: #fff;
        border-radius: 10rpx;
        > section {
          display: flex;
          align-items: center;
          justify-content: space-between;
          > div {
            &:first-of-type {
              flex: 1;
              display: flex;
              align-items: flex-start;
              .title_pic {
                image {
                  width: 160rpx;
                  height: 160rpx;
                  border-radius: 8rpx;
                }
              }
              > .good_info {
                flex: 1;
                margin: 0 20rpx;
                p {
                  margin: 0;
                  &:first-of-type {
                    font-weight: 500;
                  }
                  &:nth-of-type(2) {
                    color: #ccc;
                    font-size: 24rpx;
                    text {
                      display: block;
                      text-overflow: -o-ellipsis-lastline;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      display: -webkit-box;
                      -webkit-line-clamp: 2;
                      -webkit-box-orient: vertical;
                      margin: 6rpx 0 8rpx;
                    }
                  }
                  &:nth-of-type(3) {
                    span {
                      display: inline-block;
                      padding: 0 8rpx;
                      margin-right: 14rpx;
                      font-size: 24rpx;
                      color: rgb(241, 238, 238);
                      border-radius: 4rpx;
                      &:last-of-type {
                        background-color: #e68967;
                      }
                    }
                    .level1 {
                      background-image: linear-gradient(to right, #d13875 0%, #ec8c69 100%);
                    }
                    .level2 {
                      background-image: linear-gradient(to right, #e24d88 0%, #ec8c69 100%);
                    }
                    .level3 {
                      background-image: linear-gradient(to right, #ed6ea0 0%, #ec8c69 100%);
                    }
                  }
                  &:last-of-type {
                    margin-top: 18rpx;
                    color: rgb(197, 12, 12);
                    span {
                      margin-left: 4rpx;
                      text-decoration: line-through;
                      color: #ccc;
                    }
                  }
                }
              }
            }

            &:last-of-type {
              flex-shrink: 0;
              padding: 8rpx 20rpx;
              background-color: rgb(197, 12, 12);
              font-size: 26rpx;
              text-align: center;
              color: #fff;
              border-radius: 10rpx;
            }
          }
        }
        > div {
          display: flex;
          padding-top: 20rpx;
          margin-top: 20rpx;
          > p {
            margin: 0;
            font-size: 24rpx;
            color: #888;
            > span {
              color: #666;
            }

            &:last-of-type {
              flex: 1;
              text-align: right;
            }
          }
        }
      }
    }
  }
  .qrProup {
    /deep/ .u-mode-center-box {
      border-radius: 16rpx;
    }
    .content {
      padding: 20rpx;
      > image {
        width: 400rpx;
        height: 400rpx;
      }
      > text {
        display: block;
        margin-top: 20rpx;
        font-size: 24rpx;
        color: #999;
        text-align: center;
      }
    }
  }
}
</style>