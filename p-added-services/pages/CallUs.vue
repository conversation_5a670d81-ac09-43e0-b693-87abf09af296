<template>
    <view id="callUs">
        <main>
            <view>
                <image src="../static/images/wechat2.png" mode="widthFix" show-menu-by-longpress />
            </view>
            <text>扫码添加就可以和我们联系啦.</text>
        </main>
        <image class="bg" src="../../static/images/home/<USER>" mode="scaleToFill" />
    </view>
</template>

<script>
export default {
    name: 'CallUs',
    data() {
        return {

        };
    },
    onLoad() {

    }
};
</script>

<style lang="scss" scoped>
#callUs {
    height: 100%;
    display: flex;
    align-items: center;
    main {
        width: 100%;
        text-align: center;
        > view {
            display: flex;
            flex-direction: column;
            align-items: center;
            image {
                width: 200rpx;
                height: 200rpx;
                border-radius: 8rpx;
                margin-bottom: 60rpx;
            }
        }
        > text {
            font-size: 24rpx;
            color: #fff;
        }
    }
    .bg {
        position: absolute;
        z-index: -1;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }
}
</style>
