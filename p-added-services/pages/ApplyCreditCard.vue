<template>
    <div id="applyCreditCard">
        <main>
            <div class="header-bg">
                <image src="../static/images/applyCardBg.png" mode="widthFix" />
            </div>
            <u-sticky bg-color="transparent">
                <div class="apply-record">
                    <u-tag text="申请记录" shape="circleLeft" @click="toApplyRecord" />
                </div>
            </u-sticky>

            <div class="card-list">
                <section v-for="(c, key) in cardList" :key="key">
                    <image :src="c.logoUrl" mode="widthFix" />
                    <span>{{ c.creditName }}</span>
                    <div @click="handleApply(c)">去申请</div>
                </section>
            </div>

            <u-popup v-model="showQrcardPopup" class="qr-popup" :closeable="!!qrUrl" border-radius="20" mode="center" :mask-close-able="false">
                <div v-if="qrUrl" class="qr-popup-content">
                    <p>{{ checkedBankName }}</p>
                    <div class="qr-card">
                        <create-qrcode :val="qrUrl" :size="400" loadMake />
                    </div>

                    <div class="operate">
                        <span @click="refreshQrcard">刷新二维码</span>
                        <span @click="copyUrl">复制二维码链接</span>
                        <!--  #ifdef  APP-PLUS -->
                        <span @click="openUrl">前往浏览器打开</span>
                        <!--  #endif -->
                    </div>
                </div>
            </u-popup>
        </main>
    </div>
</template>

<script>
import CreateQrcode from 'tki-qrcode'
import { getListCardType, generateApplyLink } from '../../http/api'

export default {
    name: 'ApplyCreditCard',
    components: { CreateQrcode },
    data() {
        return {
            cardList: [],
            checkedBankName: '',
            checkedBankValue: '',
            showQrcardPopup: false,
            qrUrl: ''
        }
    },
    onLoad() {
        this.getCardList()
    },
    methods: {
        getCardList() {
            getListCardType().then(res => {
                if (res.code == '00') {
                    this.cardList = res.data || []
                }
            })
        },
        async handleApply({ creditName, creditType }) {
            this.checkedBankName = creditName
            this.checkedBankValue = creditType
            this.qrUrl = ''
            this.showQrcardPopup = true

            const { code, data } = await generateApplyLink({ creditType })
            if (code == '00') {
                this.qrUrl = data.link
            } else {
                this.showQrcardPopup = false
            }
        },
        refreshQrcard() {
            this.handleApply({ creditName: this.checkedBankName, creditType: this.checkedBankValue })
        },
        copyUrl() {
            uni.setClipboardData({
                data: this.qrUrl
            })
        },
        openUrl() {
            plus.runtime.openURL(this.qrUrl)
        },
        toApplyRecord() {
            this.$Router.push({ name: 'ApplyCreditCardRecord' })
        }
    }
}
</script>

<style lang="scss" scoped>
#applyCreditCard {
    min-height: 100%;
    background-color: #f3f5f7;
    main {
        .header-bg {
            font-size: 0;
            > image {
                width: 100%;
            }
        }
        .apply-record {
            display: flex;
            justify-content: flex-end;
            margin-top: 24rpx;
        }
        .card-list {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            padding: 24rpx 28rpx 0;
            > section {
                display: flex;
                flex-direction: column;
                align-items: center;
                width: calc(50% - 14rpx);
                height: 400rpx;
                padding: 28rpx;
                margin-bottom: 24rpx;
                border-radius: 12rpx;
                background-color: #fff;
                box-sizing: border-box;
                
                box-shadow: rgba(27, 31, 35, 0.04) 0rpx 2rpx 0rpx, rgba(255, 255, 255, 0.25) 0rpx 2rpx 0rpx inset;
                > image {
                    width: 50%;
                }
                > span {
                    overflow-y: scroll;
                    flex: 1;
                    margin: 24rpx 0;
                }

                > div {
                    width: 100%;
                    border-radius: 8rpx;
                    background-color: #1989fa;
                    color: #fff;
                    text-align: center;
                    line-height: 2em;
                }
            }
        }

        .qr-popup {
            .qr-popup-content {
                padding: 40rpx;
                > p {
                    display: block;
                    text-align: center;
                    color: #888;
                    margin: 20rpx 0;
                }
                .qr-card {
                    margin-bottom: 20rpx;
                    text-align: center;
                }
                .operate {
                    display: flex;
                    justify-content: center;
                    flex-wrap: wrap;
                    > span {
                        font-weight: 500;
                        color: #1989fa;
                        padding: 0 20rpx;
                        margin-bottom: 8rpx;
                        & + span {
                            border-left: 1px solid #c9daec;
                        }
                    }
                }
            }
        }
    }
}
</style>
