<template>
    <div id="miniProgramManagement">
        <u-cell-group :border="false">
            <u-cell-item title="特色酒水" @click="toLink('GoodsList',{appletType:'01'})" />
            <u-cell-item title="三疯茶道" @click="toLink('GoodsList',{appletType:'02'})" />
            <u-cell-item title="东山岛海产品" @click="toLink('GoodsList',{appletType:'03'})" />
            <u-cell-item title="青田泽济农资" @click="toLink('GoodsList',{appletType:'04'})" />
            <u-cell-item title="鹏农商城" @click="toLink('GoodsList',{appletType:'05'})" />
            <u-cell-item title="商品订单" @click="toLink('OrderPushList')" />
        </u-cell-group>
    </div>
</template>

<script>
export default {
    name: "MiniProgramManagement",
    data() {
        return {};
    },
    methods: {
        toLink(name, params = {}) {
            this.$Router.push({ name, params })
        }
    },
};
</script>

<style lang="less" scoped>
#miniProgramManagement {
    padding-top: 20rpx;
}
</style>