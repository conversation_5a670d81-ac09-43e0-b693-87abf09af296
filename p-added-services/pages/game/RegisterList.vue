<template>
    <div id="registerList">
        <main>

            <u-sticky>
                <header class="filter-header">
                    <u-dropdown ref="uDropdown" :close-on-click-mask="false" :close-on-click-self="false" @open="openFilterHeader">
                        <u-dropdown-item title="筛选">
                            <view class="filter-form">
                                <u-field v-model="params.playerCode" label="玩家游戏ID" placeholder="请输入玩家游戏ID" />
                                <u-field v-model="params.agentName" label="代理商名称" placeholder="请输入代理商名称" />
                                <u-field v-model="params.agentCode" label="代理商编号" placeholder="请输入代理商编号" />
                                <section class="filtrate-time">
                                    <u-field disabled :border-bottom="false" :value="startTime" placeholder="开始时间" @click="showStartPicker = true" />
                                    <span>—</span>
                                    <u-field disabled :border-bottom="false" :value="endTime" placeholder="结束时间" @click="showEndPicker = true" />
                                </section>
                                <div class="btnTools">
                                    <u-button size="medium" @click="toReset">重置</u-button>
                                    <u-button size="medium" color="#004ea9" type="primary" @click="$refs.uDropdown.close(), getPageData(true)">确定</u-button>
                                </div>
                            </view>
                        </u-dropdown-item>
                    </u-dropdown>
                </header>
            </u-sticky>

            <no-content v-if="show" />

            <view class="list-data" v-show="!show">
                <div class="card" v-for="(m, index) in pageData" :key="index">
                    <p>玩家游戏ID: <span>{{ m.playerCode }}</span></p>
                    <p>代理商名称: <span>{{ m.agentName }}</span></p>
                    <p>代理商编号: <span>{{ m.agentCode }}</span></p>
                </div>
            </view>

            <u-loadmore v-if="!show" :status="status" @loadmore="getPageData(false)" />

            <div class="addAccount" @click="showAdd = true, playerCode = ''">
                <u-icon name="plus" size="44" color="#e5e5e5" />
            </div>
            <!-- 添加绑定弹框 -->
            <u-modal ref="uModal" v-model="showAdd" title="添加玩家游戏ID" show-cancel-button async-close @confirm="toAddaddAccount">
                <u-field :label-width="160" :value="agentCode" label="当前代理商" disabled />
                <u-field :label-width="160" v-model="playerCode" label="玩家游戏ID" placeholder="请输入玩家游戏ID" />
            </u-modal>
        </main>

        <u-picker v-model="showStartPicker" title="开始时间" :show-time-tag="false" mode="time" @confirm="onConfirmStart" />
        <u-picker v-model="showEndPicker" title="结束时间" :show-time-tag="false" mode="time" @confirm="onConfirmEnd" />
    </div>
</template>

<script>
import { dateFormat } from "../../../static/utils/date";
import { gameBindList, gamePlayerBind } from "../../../http/api";

export default {
    name: "RegisterList",
    filters: {
        dateFormat,
    },
    data() {
        return {
            showAdd: false,
            status: 'loading',
            total: null,
            show: false,
            showStartPicker: false,
            showEndPicker: false,
            playerCode: "",
            oldParams: null,
            params: {
                agentName: "",
                startTime: "",
                endTime: "",
                pageNo: 1,
                pageSize: 10,
                agentCode: "",
                playerCode: "",
            },
            startTime: "",
            endTime: "",
            pageData: [],
            isDisabled: false
        };
    },
    computed: {
        agentCode() {
            return this.$store.state.userInfo.agentCode
        }
    },
    onLoad() {
        this.getPageData(true);
    },
    onReachBottom() {
        this.getPageData(false);
    },
    methods: {
        openFilterHeader() {
            this.oldParams = JSON.parse(JSON.stringify(this.params))
        },
        toAddaddAccount() {
            this.$refs.uModal.clearLoading()
            if (!this.playerCode) {
                return uni.showToast({
                    title: '玩家ID不能为空',
                    icon: 'none'
                });
            }
            gamePlayerBind(this.playerCode).then((res) => {
                if (res.code == "00") {
                    uni.showToast({
                        title: '绑定成功',
                        icon: 'none'
                    });
                    this.showAdd = false
                    this.getPageData(true);
                }
            });
        },
        toReset() {
            var oldData = this.$options.data();
            this.params = oldData.params;
            this.startTime = this.endTime = "";
        },
        onConfirmStart(val) {
            this.startTime = dateFormat(val.timestamp * 1000).substring(0, 10);
            this.showStartPicker = false;
        },
        onConfirmEnd(val) {
            this.endTime = dateFormat(val.timestamp * 1000).substring(0, 10);
            this.showEndPicker = false;
        },
        getPageData(isInquire) {
            this.params.startTime =
                this.startTime == "" ? null : this.startTime + " 00:00:00";
            this.params.endTime =
                this.endTime == "" ? null : this.endTime + " 23:59:59";

            if (!isInquire && this.status == 'nomore') return;
            if (isInquire && JSON.stringify(this.oldParams) == JSON.stringify(this.params)) {
                return;
            }
            this.status = 'loading'
            this.params.pageNo = isInquire ? 1 : this.params.pageNo + 1;
            gameBindList(this.params).then((res) => {
                if (res.code == "00") {
                    if (res.data.list.length != 0) {
                        this.show = false;
                        this.total = res.data.total;
                        !isInquire
                            ? res.data.list.forEach((i) => {
                                this.pageData.push(i);
                            })
                            : (this.pageData = res.data.list);
                        isInquire && uni.pageScrollTo({
                            scrollTop: 0,
                        });
                    } else {
                        this.show = true;
                    }
                    if (this.pageData.length >= this.total) {
                        // 数据全部加载完成
                        this.status = 'nomore';
                    } else {
                        this.status = 'loadmore';
                    }
                }
            });
        },
    }
};
</script>

<style lang="less">
@import "../../../static/css/game.less";

#registerList {
    main {
        .list-data {
            padding: 20rpx 30rpx 0;
            .card {
                background: #fff;
                border-radius: 20rpx;
                padding: 32rpx 32rpx 2rpx;
                margin-bottom: 20rpx;
                > p {
                    color: #666666;
                    font-size: 28rpx;
                    margin: 0 0 30rpx 0;
                    span {
                        color: #222222;
                    }
                }
            }
        }
        .addAccount {
            position: fixed;
            bottom: 10%;
            right: 7%;
            padding: 20rpx;
            border-radius: 50%;
            font-size: 0;
            background-color: #4a67d6;
        }
    }
}
</style>