<template>
    <div id="stir">
        <p class="interval" />
        <view style="padding-bottom:100rpx">
            <u-form :model="terminal" ref="uForm" class="u-form" :label-width='250'>
                <u-form-item label="下拨方式">
                    <u-input type="select" :select-open="stirTypesPicker" label="下拨方式" :value="stirType" @click="stirTypesPicker = true" />
                </u-form-item>
                <u-select v-model="stirTypesPicker" mode="single-column" :list="stirTypes" value-name="val" label-name="val" @confirm="stirTypeConfirm" />
                <u-form-item label="支付通道" prop="payOrgCode">
                    <u-input type="select" :select-open="payOrgCodesPicker" placeholder="请选择支付通道" :value="payOrgCode" @click="showPayOrgCodes" />
                </u-form-item>
                <u-select v-model="payOrgCodesPicker" :list="payOrgCodes" label-name="name" value-name="code" @confirm="payOrgCodeConfirm" />

                <u-form-item v-if="!rangeStir" label="选择终端">
                    <u-input type="select" :select-open="teminalNosPicker" :value="teminalAmount" @click="selectTeminalNos(true)" />
                </u-form-item>
                <!-- select teminals proup -->
                <u-popup v-if="!rangeStir" mode="bottom" v-model="teminalNosPicker">
                    <div class="u-picker__toolbar">
                        <text class="picker__cancel" @click="teminalNosPicker = false">取消</text>
                        <text class="picker__confirm" @click="selectConfirm">确认</text>
                    </div>
                    <div class="filter-bysn">
                        <u-field v-model="rStirTeminalsParams.terminalNo" label="SN编号" placeholder="输入SN编号查询">
                            <template #right>
                                <u-button size="mini" type="primary" @click="selectTeminalNos(false)">搜索</u-button>
                            </template>
                        </u-field>
                    </div>
                    <scroll-view :scroll-top="scrollTop" style="height:40vh" scroll-y="true" @scrolltolower="loadmore">
                        <checkbox-group @change="checkboxGroupChange">
                            <u-cell-group :border="false">
                                <label v-for="(t, index) in teminalNos" :key="index">
                                    <u-cell-item :border-bottom="false" :title="t" :arrow="false">
                                        <checkbox style="transform:scale(0.8)" slot="right-icon" :value="t" :checked="teminalNosSelect.length && teminalNosSelect.indexOf(t) != -1" />
                                    </u-cell-item>
                                </label>
                            </u-cell-group>
                        </checkbox-group>
                    </scroll-view>
                </u-popup>
                <view v-if="rangeStir">
                    <u-form-item label="开始终端号">
                        <u-input placeholder="请输入开始终端号" v-model="startTerminalNo" @change="getRulesCount" />
                    </u-form-item>
                    <u-form-item label="结束终端号">
                        <u-input placeholder="请输入结束终端号" v-model="endTerminalNo" @change="getRulesCount" />
                    </u-form-item>
                    <u-form-item label="终端数量">
                        <u-input type="number" placeholder="请输入终端数量" v-model="terminalCount" />
                    </u-form-item>
                </view>

                <u-form-item label="代理商">
                    <u-input placeholder="请选择下级代理商" v-model="agent" :rules="[{ required: true, message: '必选' }]" />
                        <u-icon slot="right" name="search" size="40" color="#888" @click="showAgents" />
                </u-form-item>
                <u-select v-model="agentsPicker" mode="single-column" :list="agents" value-name="val" label-name="val" @confirm="agentConfirm" />

                <view class="isJoin" v-if="agentLevel == 1 && showActCashbackRule">
                    <div class="label">是否参与未激活规则</div>
                    <div class="fild-body">
                        <u-radio-group v-model="terminal.payRuleType">
                            <u-radio :name="1">参与</u-radio>
                            <u-radio :name="0">不参与</u-radio>
                        </u-radio-group>
                    </div>
                </view>
                <section>
                    <p>结算价模板</p>
                    <div>
                        <u-form-item label="结算价模板类型">
                            <u-input placeholder="结算价模板" :value="settlePrice" disabled />
                        </u-form-item>
                    </div>
                </section>
                <section v-if="agentLevel == 1 && showActCashbackRule && showPayRuleType" key="CashbackRule">
                    <p>未激活规则</p>
                    <div>
                        <u-form-item label="活动价(元)" prop="activityPrice">
                            <u-input type="digit" placeholder="请输入活动价" v-model="terminal.activityPrice" />
                        </u-form-item>
                        <u-form-item label="原价(元)" prop="originalPrice">
                            <u-input type="digit" placeholder="请输入原价" v-model="terminal.originalPrice" />
                        </u-form-item>
                        <u-form-item label="激活周期(天)" prop="activateCycle">
                            <u-input type="number" placeholder="请输入激活周期" v-model="terminal.activateCycle" />
                        </u-form-item>
                    </div>
                </section>

                <section v-if="showSimReturnRule || showCycleReturnRule" key="SimReturnRule">
                    <p>sim卡返现规则</p>
                    <div>
                        <u-form-item v-if="showSimReturnRule" label="sim卡首次返现比例(%)" prop="simCashbackRate">
                            <u-input type="digit" placeholder="请输入首次返现比例" v-model="terminal.simCashbackRate" />
                        </u-form-item>
                        <u-form-item v-if="showCycleReturnRule" label="sim卡循环返现比例(%)" prop="cycleCashbackRate">
                            <u-input type="digit" placeholder="请输入循环返现比例" v-model="terminal.cycleCashbackRate" />
                        </u-form-item>
                    </div>
                </section>

                <section v-if="showActCashbackRule" key="ActCashbackRule">
                    <p>激活返现规则</p>
                    <div>
                        <u-form-item label="激活标准(元)" prop="freezeAmount">
                            <u-input type="digit" placeholder="请输入激活标准" v-model="terminal.freezeAmount" />
                        </u-form-item>
                        <u-form-item label="激活返现金额(元)" prop="reachActivityAmount">
                            <u-input type="digit" placeholder="请输入激活返现金额" v-model="terminal.reachActivityAmount" :rules="[{ pattern, message: '必须符合金额规则且不得超过2位小数' }]" />
                        </u-form-item>
                        <u-form-item label="冻结服务费(元)">
                            <u-input type="digit" placeholder="冻结服务费" v-model="terminal.serviceAmount" disabled />
                        </u-form-item>
                    </div>
                </section>

                <section v-if="showReaCashbackRule" key="ReaCashbackRule">
                    <p>达标返现规则</p>
                    <section class="dbfx">
                        <div v-for="(t, index) in terminal.terCashList" :key="index">
                            <p>规则{{ t.ruleType }}</p>
                            <u-form-item label="活动金额(元)" :prop="`terCashList.`+index+'.activityAmount'">
                                <u-input type="digit" placeholder="请输入活动金额" v-model="t.activityAmount" :rules="[{ pattern, message: '必须符合金额规则且不得超过2位小数' }]" />
                            </u-form-item>
                            <u-form-item label="活动天数">
                                <u-input type="number" placeholder="请输入活动天数" v-model="t.activityDays" :rules="[{ required: true, message: '必填' }]" />
                            </u-form-item>
                            <u-form-item label="达标返现金额" :border-bottom="false">
                                <u-input type="digit" placeholder="请输入达标返现金额" v-model="t.reachCashbackAmount" :rules="[{ pattern, message: '必须符合金额规则且不得超过2位小数' }]" />
                            </u-form-item>
                        </div>
                    </section>
                </section>
            </u-form>
        </view>

        <footer class="custom-button">
            <button @click="submit">确 认</button>
        </footer>
    </div>
</template>

<script>
import { getTeminalNos, getRangeStirCount, getDirRealAgent, rangeStir, selectRuleTypeCount, stir, getChannel } from "../../http/api"

const pattern = /^([1-9]\d{0,}|0|0\.\d{0,1}[1-9]|[1-9]\d{0,}\.\d{0,1}[1-9])$/;
const moneys = ['activityPrice', 'originalPrice', 'freezeAmount', 'reachActivityAmount', 'activityAmount']
const rules = {
    activateCycle: [{ required: true, message: '必填' }],
    payOrgCode: [{ required: true, message: '请选择支付通道' }],
    simCashbackRate: [{ pattern, message: '必须大于等于0、小于等于100且不得超过2位小数' }],
    cycleCashbackRate: [{ pattern, message: '必须大于等于0、小于等于100且不得超过2位小数' }],
}
moneys.forEach(m => rules[m] = [{ pattern, message: '必须符合金额规则且不得超过2位小数' }])

export default {
    name: "Stir",
    data() {
        return {
            payOrgCodesPicker: false,
            payOrgCodes: [],
            payOrgCode: "",
            stirTypesPicker: false,
            stirTypes: [{ val: "选择下拨" }, { val: "区间下拨" }],
            stirType: "选择下拨",
            rangeStir: false,
            teminalNosPicker: false,
            teminalNos: [],
            teminalNosSelect: [],
            terDoc: "",
            teminalAmount: "已选下拨终端0台",
            agentsPicker: false,
            agents: [],
            agent: "",
            settlePrice: '',
            showActCashbackRule: true,
            showReaCashbackRule: true,
            showPayRuleType: true,
            showSimReturnRule: true,
            showCycleReturnRule: true,
            pattern: /^([1-9]\d{0,}|0|0\.\d{0,1}[1-9]|[1-9]\d{0,}\.\d{0,1}[1-9])$/,
            startTerminalNo: "",
            endTerminalNo: "",
            terminalCount: "",
            terminal: {
                toAgentCode: "",
                payRuleType: 1,
                activityPrice: "",
                originalPrice: "",
                activateCycle: "",
                actReturn: "",
                freezeAmount: "",
                reachActivityAmount: "",
                serviceAmount: "",
                cashReturn: "",
                terCashList: [
                    {
                        ruleType: 1,
                        activityAmount: "",
                        activityDays: "",
                        reachCashbackAmount: "",
                    },
                ],
                simReturn: "",
                simCashbackRate: "",
                cycleCashbackRate: "",
                payOrgCode: ""
            },
            finished: false,
            total: null,
            rStirTeminalsParams: {
                payOrgCode: '',
                terminalNo: '',
                pageNo: 1,    //页数
                pageSize: 20    //每页的条数
            },
            scrollTop: 0
        };
    },
    computed: {
        agentLevel() {
            return this.$store.state.userInfo.agentLevel
        }
    },
    watch: {
        "terminal.payRuleType": {
            handler: function (a, b) {
                this.showPayRuleType = b == 1 ? false : true;
            },
        },
    },
    onReady() {
        this.$refs.uForm.setRules(rules);
    },
    methods: {
        showPayOrgCodes() {
            this.payOrgCodes = [];
            getChannel().then((res) => {
                if (res.code == "00") {
                    if (res.data.length) {
                        this.payOrgCodes = res.data
                        this.payOrgCodesPicker = true;
                    } else {
                        this.$u.toast("暂无可选支付通道！");
                    }
                }
            });
        },
        payOrgCodeConfirm([{ label, value }]) {
            this.payOrgCode = label
            this.terminal.payOrgCode = this.rStirTeminalsParams.payOrgCode = value
            this.payOrgCodesPicker = false
            this.teminalNosSelect = [];
            this.terDoc = "";
            this.teminalAmount = "已选下拨终端0台";
            this.terminal.toAgentCode = this.agent = '';
            if (this.stirType != "选择下拨") {
                this.getRulesCount()
            }
        },
        checkboxGroupChange(e) {
            this.teminalNosSelect = e.detail.value
        },
        stirTypeConfirm(val) {
            const value = val[0].value;
            this.stirType = value;
            if (value == "选择下拨") {
                this.rangeStir = false;
            } else {
                this.rangeStir = true;
            }
            this.stirTypesPicker = false;
        },
        selectTeminalNos(isInit) {
            if (!this.payOrgCode) {
                return this.$u.toast("请先选择支付通道！");
            }
            if (isInit) {
                this.rStirTeminalsParams.terminalNo = ''
            }
            this.rStirTeminalsParams.pageNo = 1
            getTeminalNos(this.rStirTeminalsParams).then((res) => {
                if (res.code == "00") {
                    if (res.data.list.length != 0) {
                        this.teminalNos = res.data.list;
                        this.total = res.data.total;
                        this.scrollTop = 0
                        if (this.teminalNos.length >= this.total) {
                            // 数据全部加载完成
                            this.finished = true;
                        } else {
                            this.finished = false;
                        }
                        this.teminalNosPicker = true;
                    }
                    else {
                        uni.showToast({
                            title: '暂无可下拨终端！',
                            icon: 'none'
                        });
                    }
                }
            });
        },
        loadmore() {
            if (this.finished) return;
            this.rStirTeminalsParams.pageNo += 1;
            getTeminalNos(this.rStirTeminalsParams).then((res) => {
                if (res.code == "00") {
                    this.total = res.data.total;
                    res.data.list.forEach((i) => {
                        this.teminalNos.push(i);
                    });
                    if (this.teminalNos.length >= this.total) {
                        // 数据全部加载完成
                        this.finished = true;
                    }
                }
            });
        },
        selectConfirm() {
            this.teminalAmount = "已选下拨终端" + this.teminalNosSelect.length + "台";
            if (this.teminalNosSelect.length != 0) {
                this.terDoc = "";
                this.teminalNosSelect.forEach((i) => {
                    if (this.terDoc == "") {
                        this.terDoc = i;
                    } else {
                        this.terDoc = this.terDoc + "," + i;
                    }
                });
            }
            this.teminalNosPicker = false;
        },
        getRulesCount() {
            if (this.stirType == "选择下拨") {
                if (this.terDoc != "" && this.terminal.toAgentCode != "" && this.terminal.payOrgCode != "") {
                    getRangeStirCount({
                        payOrgCode: this.terminal.payOrgCode,
                        toAgentCode: this.terminal.toAgentCode,
                        terDoc: this.terDoc
                    }).then((res) => {
                        if (res.code == "00") {
                            this.afterGetCount(res.data);
                        } else {
                            this.teminalNosSelect = [];
                            this.terDoc = "";
                            this.teminalAmount = "已选下拨终端0台";
                        }
                    });
                }
            } else {
                if (
                    this.startTerminalNo != "" &&
                    this.endTerminalNo != "" &&
                    this.terminal.toAgentCode != "" &&
                    this.terminal.payOrgCode != ""
                ) {
                    selectRuleTypeCount({
                        startTerminalNo: this.startTerminalNo,
                        endTerminalNo: this.endTerminalNo,
                        toAgentCode: this.terminal.toAgentCode,
                        payOrgCode: this.terminal.payOrgCode,
                    }).then((res) => {
                        if (res.code == "00") {
                            this.afterGetCount(res.data);
                        } else {
                            this.startTerminalNo = "";
                            this.endTerminalNo = "";
                            this.terminal.toAgentCode = "";
                        }
                    });
                }
            }
        },
        afterGetCount(data) {
            this.settlePrice = data.modelName;
            this.terminal.simReturn = data.simReturn;
            this.terminal.cycleReturn = data.cycleReturn;
            this.terminal.actReturn = data.actReturn;
            this.terminal.cashReturn = data.cashReturn;
            if (data.simReturn == 0) {
                this.showSimReturnRule = true;
                this.terminal.simCashbackRate = data.simDto.simCashbackRate;
            } else {
                this.terminal.simCashbackRate = ''
                this.showSimReturnRule = false;
            }
            if (data.cycleReturn == 0) {
                this.showCycleReturnRule = true;
                this.terminal.cycleCashbackRate = data.simDto.cycleCashbackRate;
            } else {
                this.terminal.cycleCashbackRate = ''
                this.showCycleReturnRule = false;
            }
            if (data.actReturn == 0) {
                this.showActCashbackRule = true;
                this.terminal.freezeAmount = data.actDto.freezeAmount;
                this.terminal.reachActivityAmount = data.actDto.reachActivityAmount;
                this.terminal.serviceAmount = data.actDto.serviceAmount;
            } else {
                this.showActCashbackRule = false;
                this.terminal.payRuleType = 0;
            }
            if (data.cashReturn == 0) {
                this.showReaCashbackRule = true;
                this.terminal.terCashList = [];
                data.cashList.forEach((i) => {
                    this.terminal.terCashList.push({
                        ruleType: i.ruleType,
                        activityAmount: i.activityAmount,
                        activityDays: i.activityDays,
                        reachCashbackAmount: i.reachCashbackAmount,
                    });
                });
            } else {
                this.showReaCashbackRule = false;
            }
        },
        showAgents() {
            if (this.stirType == "选择下拨") {
                if (this.terDoc == "") {
                    this.$u.toast("请先选择终端！");
                    return;
                }
            }
            this.agents = [];
            getDirRealAgent(this.agent).then((res) => {
                if (res.code == "00") {
                    if (res.data.length != 0) {
                        res.data.forEach((i) => {
                            this.agents.push({ val: i.name + "（" + i.agentCode + "）" });
                        });
                        this.agentsPicker = true;
                    } else {
                        uni.showToast({ title: '暂无下级代理商！', icon: 'none' });
                    }
                }
            });
        },
        agentConfirm(val) {
            const value = val[0].value;
            const bracketStart = value.lastIndexOf("（")
            const bracketEnd = value.lastIndexOf("）")

            this.agent = value.substring(0, bracketStart)
            this.terminal.toAgentCode = value.substring(bracketStart + 1, bracketEnd)
            this.getRulesCount();
            this.agentsPicker = false;
        },
        submit() {
            this.$refs.uForm.validate(valid => {
                if (valid) {
                    if (this.stirType == "选择下拨") {
                        if (this.terDoc == "") {
                            return uni.showToast({ title: '终端未选择！', icon: 'none' });
                        }
                    } else {
                        if (
                            (!/^[0-9]+$/.test(this.startTerminalNo) ||
                                !/^[0-9]+$/.test(this.endTerminalNo)) &&
                            (this.startTerminalNo != this.endTerminalNo ||
                                this.terminalCount != 1)
                        ) {
                            uni.showToast({ title: '终端编号不为纯数字时只能单台下拨！', icon: 'none' })
                            return;
                        }
                    }
                    if (this.$store.state.userInfo.agentLevel == 1 && this.showActCashbackRule && this.showPayRuleType) {
                        if (
                            parseFloat(this.terminal.activityPrice) >
                            parseFloat(this.terminal.originalPrice)
                        ) {
                            uni.showToast({ title: '未激活规则活动价必须小于等于原价！', icon: 'none' })
                            return;
                        }
                    }
                    if (this.stirType == "选择下拨") {
                        rangeStir(Object.assign(this.terminal, { terDoc: this.terDoc })).then(
                            (res) => {
                                if (res.code == "00") {
                                    uni.showToast({ title: res.message, icon: 'none' })
                                    setTimeout(() => {
                                        this.$Router.back(1);
                                    }, 1000);
                                }
                            }
                        );
                    } else {
                        stir(
                            Object.assign(this.terminal, {
                                startTerminalNo: this.startTerminalNo,
                                endTerminalNo: this.endTerminalNo,
                                terminalCount: this.terminalCount,
                            })
                        ).then((res) => {
                            if (res.code == "00") {
                                uni.showToast({ title: res.message, icon: 'none' })

                                setTimeout(() => {
                                    this.$Router.back(1);
                                }, 1000);
                            }
                        });
                    }
                }
            })
        },
    },
};
</script>

<style lang="less" scoped>
#stir {
    background-color: #fff;
    .u-form {
        /deep/ .u-form-item {
            padding: 20rpx 28rpx;
            line-height: 0;
            input {
                height: 48rpx;
                min-height: 48rpx !important;
            }
        }
        > section {
            > p {
                margin: 0;
                padding: 2vw 4.26667vw;
                color: #222222;
                font-size: 26rpx;
                background-color: #f3f5f7;
            }
            > section {
                > div {
                    &:not(:last-of-type) {
                        margin-bottom: 20rpx;
                    }
                    > p {
                        background-color: white;
                        border-radius: 10rpx 10rpx 0 0;
                        padding: 20rpx 32rpx;
                        margin: 0;
                    }
                }
            }

            .dbfx {
                background-color: #fff;
                padding: 30rpx;
                > div {
                    background: #f3f5f7;
                    border-radius: 20rpx;
                    > p {
                        padding: 2vw 0;
                        margin: 0 4.26667vw;
                        background-color: transparent;
                        border-bottom: 2rpx solid #e5e5e5;
                    }
                }
            }
        }
    }
    .isJoin {
        display: flex;
        align-items: center;
        padding: 20rpx 28rpx;
        .label {
            width: 300rpx;
            color: black;
        }
        .fild-body {
            flex: 1;
        }
    }
    .custom-button {
        position: fixed;
        bottom: 0;
        margin-top: 20rpx;
        z-index: 9;
    }

    .u-picker__toolbar {
        display: flex;
        justify-content: space-between;
        padding: 26rpx 32rpx;
        border-bottom: 2rpx soild #e5e5e5;
        .picker__confirm {
            color: #576b95;
        }
    }
    .error-message {
        display: block;
        font-size: 24rpx;
        line-height: 24rpx;
        color: #fa3534;
        margin-top: 12rpx;
    }
    /deep/ checkbox .wx-checkbox-input {
        border-radius: 50% !important;
    }

    /deep/ checkbox .wx-checkbox-input.wx-checkbox-input-checked {
        color: #004ea9;
    }
}
</style>