<template>
    <div id="machineDetails">
        <main>
            <div>
                <p>
                    <span>机具型号:</span>
                    <span>{{ machine.terminalType }}</span>
                </p>
                <p>
                    <span>机具终端号:</span>
                    <span><text  user-select selectable>{{ machine.terminalNo }} </text></span>
                </p>
                <p>
                    <span>代理商名称:</span>
                    <span>{{ machine.realName }}</span>
                </p>
                <p v-if="machine.merchantName != null">
                    <span>商户名称:</span>
                    <span>{{ machine.merchantName }}</span>
                </p>
                <p v-if="machine.merchantId != null">
                    <span>商户编号:</span>
                    <span><text  user-select selectable>{{ machine.merchantId }}</text></span>
                </p>
                <p v-if="machine.tranTime != null">
                    <span>交易时间:</span>
                    <span>{{ machine.tranTime }}</span>
                </p>
            </div>
            <div>
                <p>
                    <span>是否参与激活返现:</span>
                    <span>{{ machine.isActivation == 0 ? "参与" : "不参与" }}</span>
                </p>
            </div>
            <div>
                <p v-if="machine.isActivation == 0 && machine.activationTime != null">
                    <span>激活时间:</span>
                    <span>{{ machine.activationTime }}</span>
                </p>
                <p>
                    <span>是否参与达标返现:</span>
                    <span>{{ machine.isDeposit == 0 ? "参与" : "不参与" }}</span>
                </p>
                <p v-if="machine.isDeposit == 0">
                    <span>达标交易累计金额:</span>
                    <span>{{ machine.depositTranAmount | toDecimal2 }}</span>
                </p>
            </div>
        </main>
    </div>
</template>

<script>
import { getMechineDetail } from "../../../http/api";
import { toDecimal2 } from '../../../static/utils/date';

export default {
    name: "MachineDetails",
    filters: {
        toDecimal2
    },
    data() {
        return {
            machine: {
                terminalType: "",
                terminalNo: "",
                realName: "",
                merchantName: "",
                merchantId: "",
                tranTime: "",
                isActivation: 0,
                activationTime: "",
                isDeposit: 0,
                depositTranAmount: "",
            },
        };
    },
    onLoad() {
        getMechineDetail({
            terminalNo: this.$Route.query.terminalNo,
            agentCode: this.$Route.query.agentCode,
        }).then((res) => {
            if (res.code == "00") {
                this.machine = res.data;
            }
        });
    },
};
</script>

<style lang="less" scoped>
#machineDetails {
    height: 100%;
    padding-top: 20rpx;
    main {
        > div {
            background-color: white;
            padding: 0.2rpx 30rpx 0;
            margin-bottom: 20rpx;
            > p {
                display: flex;
                border-bottom: 1px solid #f3f5f7;
                padding: 20rpx 0;
                position: relative;
                &:last-of-type {
                    border: 0;
                }
                > span {
                    &:first-of-type {
                        flex-shrink: 0;
                        margin-right: 0.5em;
                        color: #888;
                    }
                    &:nth-of-type(2) {
                        flex: 1;
                        color: #333;
                    }
                }
            }
        }
    }
}
</style>