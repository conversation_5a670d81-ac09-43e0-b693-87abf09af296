<template>
    <div id="equipmentManagement">
        <main>
            <no-content v-show="show" />

            <section v-show="!show" class="list">
                <div v-for="(t, index) in pageData" :key="index">
                    <div class="item-l">
                        <image src="@/static/images/home/<USER>" alt="" />
                    </div>
                    <div class="item-r">
                        <div>
                            <span>机具型号：<span>{{ t.terminalType }}</span></span>
                            <span></span>
                        </div>
                        <div>
                            <p>
                                <span>机具总数: </span>
                                <span>{{ t.terminalAll }}</span>
                            </p>
                            <p>
                                <span>机具激活: </span>
                                <span>{{ t.terminalActivity }}</span>
                            </p>
                            <p>
                                <span>机具达标: </span>
                                <span>{{ t.terminalDeposit }}</span>
                            </p>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>
</template>

<script>
import { getTerminalTypeList } from "../../../http/api";

export default {
    name: "EquipmentManagement",
    data() {
        return {
            show: false,
            pageData: []
        };
    },
    onLoad() {
        this.getPageData();
    },
    methods: {
        getPageData() {
            getTerminalTypeList().then((res) => {
                if (res.code == "00") {
                    if (res.data.length != 0) {
                        this.show = false;
                        this.pageData = res.data;
                    } else {
                        this.show = true;
                    }
                }
            });
        },
        modelDetails(terminalType) {
            this.$Router.push({ name: 'ModelDetails', params: { terminalType } });
        }
    }
};
</script>

<style lang="less" scoped>
#equipmentManagement {
    height: 100%;
    padding-top: 20rpx;
    main {
        min-height: 100%;
        background-color: #fff;
        > section {
            > div {
                display: flex;
                align-items: flex-start;
                justify-content: space-between;
                background-color: #fff;
                padding: 0 30rpx;
                .item-l {
                    padding: 26rpx 0;
                    > image {
                        width: 68rpx;
                        height: 68rpx;
                    }
                }
                .item-r {
                    flex: 1;
                    margin-left: 20rpx;
                    padding: 26rpx 0;
                    border-bottom: 1rpx solid #e5e5e5;
                    > div {
                        &:first-child {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-bottom: 20rpx;
                            > span {
                                &:last-child {
                                    font-size: 24rpx;
                                    color: #004ea9;
                                }
                                > span {
                                    font-weight: bold;
                                }
                            }
                        }
                        &:last-child {
                            display: flex;
                            justify-content: space-around;
                            border-radius: 12rpx;
                            padding: 14rpx 0;
                            background: #f3f5f7;
                            color: #666666;
                            font-size: 24rpx;
                            p {
                                margin: 0;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>