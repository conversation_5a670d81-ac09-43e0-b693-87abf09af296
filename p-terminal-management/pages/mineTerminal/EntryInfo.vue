<template>
    <div>
        <nav>
            <!-- <span>入账流水</span> -->
            <span>入账时间</span>
            <span>入账状态</span>
            <span>返现金额</span>
        </nav>
        <main>
            <section v-for="(i,index) in list " :key="index">
                <!-- <span>{{i.reqFlowNo}}</span> -->
                <span>{{i.enterTime && dateFormat(i.enterTime)}}</span>
                <span :class="statusColor[i.postStatus]">{{postStatus[i.postStatus]}}</span>
                <span>{{i.returnAmount}}</span>
            </section>
        </main>
    </div>
</template>

<script>
import { dateFormat } from "../../../static/utils/date.js";
import { getTerSimFlowDetail } from '../../../http/api'

export default {
    data() {
        return {
            list: [],
            postStatus: ['初始状态', '成功', '失败'],
            statusColor: ['blue', 'green', 'red']
        };
    },
    onLoad() {
        this.getListData()
    },
    methods: {
        async getListData() {
            const { data } = await getTerSimFlowDetail(this.$Route.query.id)
            this.list = data.list || []
        },
        dateFormat
    }
};
</script>

<style lang="less" scoped>
nav {
    height: 100rpx;
    border-bottom: 1px solid #8799a3;
    span {
        font-weight: 500;
    }
}
nav,
section {
    display: flex;
    align-items: center;
    > span {
        flex: 1;
        text-align: center;
    }
}
main {
    overflow-y: scroll;
    height: calc(100vh - 100rpx);
    > section {
        padding: 20rpx 0;
        &:nth-child(odd) {
            background-color: #fff;
        }
        > span {
            padding: 0 8rpx;
        }
    }
}
.blue {
    color: #0081ff;
}
.green {
    color: #39b54a;
}
.red {
    color: #e54d42;
}
</style>
