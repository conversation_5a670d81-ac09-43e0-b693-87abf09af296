<template>
  <div class="container">
    <main>
      <div class="qrCard">
        <canvas
          v-if="!imageUrl"
          style="width: 100%; height: 1000px; visibility: hidden"
          canvas-id="qrImage222"
          id="qrImage222"
        ></canvas>

        <div v-else class="image-container">
          <image
            :src="imageUrl"
            mode="widthFix"
            @longpress="openAppToolBox(imageUrl)"
            show-menu-by-longpress
          />
        </div>

        <div
          class="qrCard-content"
          v-if="qrUrl && !imageUrl"
          style="visibility: hidden"
        >
          <create-qrcode
            :val="qrUrl"
            :size="340"
            loadMake
            @result="resultQr"
            :showLoading="false"
          />
        </div>
      </div>
    </main>
  </div>
</template>

<script>
  import CreateQrcode from 'tki-qrcode';
  import { getMerchantQrCode } from '../../../http/api';

  export default {
    components: { CreateQrcode },
    props: {
        qrUrl: String,
      terminalNo: String,
      payOrgCode: String
    },
    data() {
      return {
        imageUrl: '',
        descImgs: []
      };
    },
    created() {
    //   this.getQrUrl()
    },
    methods: {
      draw(qrImgUrl) {
        const ctx = uni.createCanvasContext('qrImage222', this);
        const { windowWidth } = uni.getSystemInfoSync();
        uni.getImageInfo({
          src: '../../static/images/wsy-code-plate-bg.png',
          success: ({ width, height }) => {
            // 背景
            ctx.drawImage(
              require('../../static/images/wsy-code-plate-bg.png'),
              0,
              0,
              windowWidth,
              (height * windowWidth) / width
            );
            //二维码
            ctx.drawImage(
              qrImgUrl,
              windowWidth / 2 - uni.upx2px(340) / 2,
              uni.upx2px(320),
              uni.upx2px(340),
              uni.upx2px(340)
            );
            //文字
            ctx.setFontSize(uni.upx2px(28));
            ctx.setFillStyle('#333333');
            ctx.setTextAlign('center');
            ctx.fillText(
             this.terminalNo,
              uni.upx2px(380),
              uni.upx2px(700)
            );
            ctx.draw(false, () => {
              uni.canvasToTempFilePath(
                {
                  x: 0,
                  y: 0,
                  width: windowWidth,
                  height: (height * windowWidth) / width,
                  canvasId: 'qrImage222',
                  success: (res) => {
                    console.log('res', res);
                    this.imageUrl = res.tempFilePath;
                  },
                  fail: (err) => {
                    console.log('error', err);
                  }
                },
                this
              );
            });
          }
        });
      },
      getQrUrl() {
        getMerchantQrCode({
          terminalNo: this.terminalNo,
          payOrgCode: this.payOrgCode
        }).then((res) => {
          if (res.code == '00') {
            if (res.data) {
              this.qrUrl = res.data;
            } else {
              this.$u.toast('无可用于生成二维码的链接!');
            }
          }
        });
      },
      resultQr(qrImgUrl) {
        this.$nextTick(() => {
          this.draw(qrImgUrl);
        });
      },
      openAppToolBox(imgPath) {
        // #ifdef APP-PLUS
        uni.showActionSheet({
          itemList: ['保存'],
          success: function ({ tapIndex }) {
            switch (tapIndex) {
              case 0:
                uni.saveImageToPhotosAlbum({
                  filePath: imgPath,
                  success: function () {
                    uni.showToast({ title: '图片保存成功' });
                  }
                });
                break;
              default:
                break;
            }
          }
        });
        // #endif
      }
    }
  };
</script>

<style lang="less" scoped>
  .container {
    main {
      .desc-img {
        font-size: 0;
      }

      .qrCard {
        width: 100%;
        font-size: 0;

        .image-container {
          padding: 0 10vw;
          > image {
            width: 100%;
          }

        }
      }
    }
  }
</style>
