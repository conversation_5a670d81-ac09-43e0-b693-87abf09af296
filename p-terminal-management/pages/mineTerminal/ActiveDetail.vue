<template>
    <div>
        <main>
            <p>
                <span>支付通道</span>
                <span>{{$Route.query.payOrgCode | orgCodeFormat}}</span>
            </p>
            <p>
                <span>终端号</span>
                <span><text  user-select selectable>{{info.terminalNo}}</text></span>
            </p>
            <p>
                <span>终端型号</span>
                <span>{{info.terminalType}}</span>
            </p>
            <div></div>
            <p>
                <span>激活标准</span>
                <span>{{info.activateStanderAmount}}</span>
            </p>
            <p>
                <span>服务费</span>
                <span>{{info.serviceAmount}}</span>
            </p>
            <p>
                <span>激活截止日期</span>
                <span>{{info.activateLinedTime ? dateFormat(info.activateLinedTime) : '-'}}</span>
            </p>
            <p>
                <span>激活状态</span>
                <span class="status">{{activateStatus[info.activateStatus]}}</span>
            </p>
            <p>
                <span>激活时间</span>
                <span>{{info.activateTime ?  dateFormat(info.activateTime) : '-'}}</span>
            </p>
            <div></div>
            <p>
                <span>缴费金额</span>
                <span>{{info.payAmount === 0 ? 0 : info.payAmount ? info.payAmount : '-' }}</span>
            </p>
            <p>
                <span>缴费状态</span>
                <span class="status">{{payStatus[info.payStatus]}}</span>
            </p>
            <p>
                <span>更新时间</span>
                <span>{{info.updateTime ? dateFormat(info.updateTime) : '-'}}</span>
            </p>
            <div></div>
            <p>
                <span>返现状态</span>
                <span class="status">{{cashBackStatus[info.cashBackStatus]}}</span>
            </p>
            <p>
                <span>返现金额</span>
                <span>{{info.returnAmount === 0 ? 0 : info.returnAmount ? info.returnAmount : '-'}}</span>
            </p>
            <div></div>
            <p>
                <span>入账状态</span>
                <span class="status">{{postStatus[info.postStatus]}}</span>
            </p>
            <p>
                <span>入账时间</span>
                <span>{{info.enterTime ? dateFormat(info.enterTime) : '-'}}</span>
            </p>
        </main>
    </div>
</template>

<script>
import { getTerActivateDetail } from '../../../http/api'
import { dateFormat } from "../../../static/utils/date";

export default {
    data() {
        return {
            info: {},
            activateStatus: ['未激活', '已激活'],
            payStatus: ['未缴费', '缴费中', '缴费成功', '缴费失败'],
            cashBackStatus: ['已返现', '未返现'],
            postStatus: ['初始状态', '成功', '失败']
        };
    },
    onLoad() {
        this.getInfo()
    },
    methods: {
        async getInfo() {
            const { data } = await getTerActivateDetail(this.$Route.query.id)
            this.info = data || {}
        },
        dateFormat
    }
};
</script>

<style lang="less" scoped>
main {
    min-height: 100vh;
    padding-top: 20rpx;
    background-color: #f3f5f7;
    > p {
        display: flex;
        justify-content: space-between;
        margin: 0;
        padding: 28rpx;
        background-color: #fff;
        border-bottom: 2rpx solid #f3f5f7;
        > span {
            &:first-of-type {
                color: #8799a3;
            }
        }
        .status {
            color: #a5673f;
        }
    }
    > div {
        height: 20rpx;
    }
}
</style>
