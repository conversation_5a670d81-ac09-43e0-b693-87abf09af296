<template>
    <div id="notAndFakeActiveDetail">
        <main>
            <view class="filter" @click="openAside">
                <text class="u-m-r-10" :class="{'has-filter':isHasTerm}">筛选</text>
                <u-icon name="arrow-down" :color="isHasTerm ? '#4a67d6' : ''" />
            </view>

            <u-sticky>
                <header>
                    <p>{{agentName || '未知'}}</p>
                </header>
            </u-sticky>

            <section>
                <no-content v-if="show" />
                <view v-show="!show" class="list-data">
                    <section v-for="(i,index) in listData" :key="index">
                        <section>
                            <div>
                                <image src="../../static/images/signaturePos.png" alt="" />
                                <span>{{i.terminalType}}</span>
                            </div>
                            <div>
                                <p><strong><text  user-select selectable>{{i.terminalNo}}</text> ({{i.payOrgCode | orgCodeFormat}})</strong></p>
                                <p>考核日期:{{i.cutdownDay}}</p>
                                <div v-if="i.type == 2">
                                    <span :class="{'reach':!(Number(i.amount)  < Number(i.tradeVolume))}" :style="{'width': Number(i.amount)  < Number(i.tradeVolume) ? amountFormat(i.amount,i.tradeVolume)+'%' : '100%'}"></span>
                                    <p>{{i.amount}}</p>
                                    <p>{{i.tradeVolume}}</p>
                                </div>
                            </div>
                        </section>
                        <section>
                            <div>
                                <span>倒计时</span>
                                <span>{{i.lineTime}}天</span>
                            </div>
                            <p>{{i.type == 1 ?"未激活" : i.type == 2 ?"伪激活" :''}}</p>
                        </section>
                    </section>
                </view>
                <u-loadmore v-if="!show" :status="status" @loadmore="loadmore" />
            </section>
        </main>
        <u-back-top :scrollTop="scrollTop" />

        <!-- 筛选 -->
        <u-popup v-model="showAside" mode="right" :mask-close-able="false">
            <main class="filter-content">
                <section>
                    <p>类型</p>
                    <u-radio-group v-model="ajaxParams.type">
                        <u-radio :name="null" :class="{'checked':ajaxParams.type == null}">全部</u-radio>
                        <u-radio :name="1" :class="{'checked':ajaxParams.type == 1}">未激活</u-radio>
                        <u-radio :name="2" :class="{'checked':ajaxParams.type == 2}">伪激活</u-radio>
                    </u-radio-group>
                </section>
                <section>
                    <p>未激活预警</p>
                    <div :class="{'disabledSelect':type == 2}">
                        <u-field type="digit" placeholder="倒计时开始天数" v-model="ajaxParams.unStartDay" :disabled="type == 2" :border-bottom="false" :clearable="false" />
                        <span></span>
                        <u-field type="digit" placeholder="倒计时终止天数" v-model="ajaxParams.unEndDay" :disabled="type == 2" :border-bottom="false" :clearable="false" />
                    </div>
                </section>
                <section>
                    <p>伪激活预警</p>
                    <div :class="{'disabledSelect':type == 1}">
                        <u-field type="digit" placeholder="倒计时开始天数" v-model="ajaxParams.unActivaStartDay" :border-bottom="false" :disabled="type == 1" :clearable="false" />
                        <span></span>
                        <u-field type="digit" placeholder="倒计时终止天数" v-model="ajaxParams.unAcitvaUnEndDay" :border-bottom="false" :disabled="type == 1" :clearable="false" />
                    </div>
                </section>
                <section>
                    <p>支付通道</p>
                    <u-field :border-bottom="false" placeholder="选择支付通道" :value="payOrgCode" @click="showPayOrgCodes" :right-icon="`arrow-${payOrgCodesPicker ? 'up' : 'down'}`" disabled />
                </section>
                <section>
                    <p>终端序列号</p>
                    <u-field placeholder="请输入终端序列号" v-model="ajaxParams.terminalNo" :border-bottom="false" />
                </section>
                <footer>
                    <div @click="reset">重置</div>
                    <p></p>
                    <div @click="queryByParams">确定</div>
                </footer>
            </main>

        </u-popup>
        <u-select v-model="payOrgCodesPicker" :list="payOrgCodes" label-name="name" value-name="code" @confirm="payOrgCodeConfirm" :default-value="payOrgCode ? [payOrgCodes.findIndex(i=>i.name === payOrgCode)] : []" />
    </div>
</template>

<script>
import { getTerminalActiviList, getChannel } from '../../../http/api'

export default {
    name: 'NotAndFakeActiveDetail',
    data() {
        return {
            payOrgCodesPicker: false,
            payOrgCodes: [],
            payOrgCode: "",
            isHasTerm: false,
            status: 'loading',
            total: null,
            show: false,
            showAside: false,
            ajaxParams: {
                payOrgCode: '',
                agentCode: '',
                terminalNo: '', //终端号
                type: null, //类型  1未激活 2 伪激活  全部为null 
                unStartDay: '', //未激活预警开始天数 当选择伪激活时不可填写
                unEndDay: '',//未激活预警结束天数  当选择伪激活时不可填写
                unActivaStartDay: '', //伪激活预警开始天数 当选择未激活时不可填写
                unAcitvaUnEndDay: '',//伪激活预警结束天数 当选择未激活时不可填写
                agentType: 0, //查询类型 0 本级代理商，1 下级代理商
                pageNo: 1,//当前页
                pageSize: 10 //当前条数
            },
            oldAjaxParams: null,
            listData: [],
            agentName: '',
            scrollTop: 0
        };
    },
    computed: {
        type() {
            return this.ajaxParams.type;
        }
    },
    watch: {
        type(val) {
            switch (val) {
                case 1:
                    this.ajaxParams.unActivaStartDay = this.ajaxParams.unAcitvaUnEndDay = '';
                    break;
                case 2:
                    this.ajaxParams.unStartDay = this.ajaxParams.unEndDay = '';
                    break;
                default:
                    break;
            }
        }

    },
    onLoad() {
        this.agentName = this.$Route.query.agentName
        this.getPageData()
    },
    onPageScroll(e) {
        this.scrollTop = e.scrollTop;
    },
    onReachBottom() {
        this.loadmore()
    },
    methods: {
        showPayOrgCodes() {
            this.payOrgCodes = [];
            getChannel().then((res) => {
                if (res.code == "00") {
                    if (res.data.length) {
                        this.payOrgCodes = [{ name: "全部", code: '' }, ...res.data]
                        this.payOrgCodesPicker = true;
                    } else {
                        this.$u.toast("暂无可选支付通道！");
                    }
                }
            });
        },
        payOrgCodeConfirm([{ label, value }]) {
            this.payOrgCode = label === '全部' ? '' : label
            this.ajaxParams.payOrgCode = value
            this.payOrgCodesPicker = false
        },
        amountFormat(amount, tradeVolume) {
            return Math.round(amount) / Math.round(tradeVolume) * 100;
        },
        openAside() {
            this.oldAjaxParams = JSON.parse(JSON.stringify(this.ajaxParams));
            this.showAside = true;
        },
        reset() {
            this.ajaxParams = this.$options.data().ajaxParams;
            this.payOrgCode = ''
        },
        queryByParams() {
            this.getPageData();
            this.showAside = false;
        },
        getPageData() {
            if (JSON.stringify(this.oldAjaxParams) == JSON.stringify(this.ajaxParams)) {
                return;
            }
            this.ajaxParams.agentType = this.$Route.query.agentType;
            this.ajaxParams.agentCode = this.$Route.query.agentCode;
            this.ajaxParams.pageNo = 1;
            this.status = 'loading'
            getTerminalActiviList(this.ajaxParams).then((res) => {
                if (res.code == "00") {
                    if (res.data.list.length != 0) {
                        this.show = false;
                        this.total = res.data.total;
                        this.listData = res.data.list;
                        uni.pageScrollTo({
                            scrollTop: 0
                        });
                        if (this.listData.length >= this.total) {
                            // 数据全部加载完成
                            this.status = 'nomore';
                        } else {
                            this.status = 'loadmore';
                        }
                    } else {
                        this.show = true;
                    }
                }
            });
        },
        loadmore() {
            if (this.status == 'nomore') return;
            this.status = 'loading'
            this.ajaxParams.pageNo = this.ajaxParams.pageNo + 1;
            getTerminalActiviList(this.ajaxParams).then((res) => {
                if (res.code == "00") {
                    this.total = res.data.total;
                    res.data.list.forEach((i) => {
                        this.listData.push(i);
                    });
                    this.loading = false;
                    if (this.listData.length >= this.total) {
                        // 数据全部加载完成
                        this.status = 'nomore';
                    } else {
                        this.status = 'loadmore';
                    }
                }
            });
        }
    }
};
</script>

<style lang="less" scoped>
#notAndFakeActiveDetail {
    height: 100%;
    > main {
        min-height: 100%;
        .filter {
            text-align: center;
            line-height: 80rpx;
            border-bottom: 1rpx solid #e5e5e5;
            background-color: #fff;
            .has-filter {
                color: #4a67d6;
            }
        }
        header {
            padding: 20rpx 30rpx;
            background-color: #f3f5f7;
            > p {
                height: 40rpx;
                font-weight: 450;
                vertical-align: middle;
                &::before {
                    content: "";
                    display: inline-block;
                    width: 12rpx;
                    height: 12rpx;
                    margin: 0 10rpx 4rpx 0;
                    border-radius: 50%;
                    background-color: rgb(42, 123, 230);
                }
            }
        }
        > section {
            margin: 0 20rpx;
            .list-data {
                > section {
                    display: flex;
                    padding: 0 20rpx;
                    margin-bottom: 24rpx;
                    border-radius: 14rpx;
                    background-color: #fff;
                    box-shadow: 0 2rpx 10rpx 0 rgba(0, 0, 0, 0.1);
                    > section {
                        &:first-of-type {
                            flex: 1;
                            display: flex;
                            align-items: center;
                            padding: 24rpx;
                            padding-left: 0;
                            > div {
                                &:first-of-type {
                                    display: flex;
                                    flex-direction: column;
                                    align-items: center;
                                    color: #666;
                                    font-size: 26rpx;
                                    > image {
                                        width: 88rpx;
                                        height: 88rpx;
                                        margin-bottom: 30rpx;
                                    }
                                }
                                &:last-of-type {
                                    flex: 1;
                                    margin-left: 20rpx;
                                    > p {
                                        margin: 0;
                                        margin-bottom: 24rpx;
                                        word-break: break-all;
                                        &:last-of-type {
                                            font-size: 26rpx;
                                            color: #666;
                                        }
                                    }
                                    > div {
                                        position: relative;
                                        width: 100%;
                                        height: 32rpx;
                                        border-radius: 32rpx;
                                        border: 2rpx solid #e5e5e5;
                                        > span {
                                            display: block;
                                            height: 28rpx;
                                            border-radius: 32rpx;
                                            background: linear-gradient(
                                                    -70deg,
                                                    transparent 28rpx,
                                                    rgb(240, 157, 4) 0
                                                )
                                                bottom right;
                                        }
                                        .reach {
                                            background: rgb(240, 157, 4);
                                        }
                                        > p {
                                            position: absolute;
                                            top: 50%;
                                            margin: 0;
                                            transform: translateY(-50%);
                                            color: #333;
                                            font-size: 24rpx;
                                            &:first-of-type {
                                                left: 16rpx;
                                            }
                                            &:last-of-type {
                                                right: 16rpx;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        &:last-of-type {
                            flex-shrink: 0;
                            display: flex;
                            flex-direction: column;
                            justify-content: space-between;
                            > div {
                                display: flex;
                                flex-direction: column;
                                align-items: center;
                                justify-content: center;
                                width: 130rpx;
                                padding: 4rpx;
                                color: #fff;
                                background-color: rgb(248, 164, 8);
                                border-radius: 8rpx;
                                font-size: 24rpx;
                            }
                            > p {
                                text-align: right;
                                font-size: 26rpx;
                                color: rgb(218, 119, 6);
                                margin-bottom: 4rpx;
                            }
                        }
                    }
                }
            }
        }
    }
    .filter-content {
        display: flex;
        flex-direction: column;
        width: 76vw;
        padding: 0 30rpx;
        /deep/ .u-label {
            display: none !important;
        }
        /deep/ .u-radio-group {
            display: flex;
            justify-content: space-between;
            .u-radio {
                padding: 0 24rpx;
                background-color: #f3f5f7;
                border-radius: 6rpx;
                line-height: 1.9;
            }
            .u-radio__icon-wrap {
                display: none;
            }
            .u-radio__label {
                margin: 0;
                font-size: 28rpx;
            }
            .checked {
                .u-radio {
                    background-color: #4a67d6;
                }
                .u-radio__label {
                    color: #fff;
                }
            }
        }

        > section {
            > p {
                padding: 40rpx 0 20rpx;
                color: #666;
            }
            &:nth-of-type(2),
            &:nth-of-type(3) {
                > div {
                    display: flex;
                    align-items: center;
                    border-radius: 8rpx;
                    > span {
                        width: 40rpx;
                        height: 2rpx;
                        margin: 0 10rpx;
                        background-color: #d3d1d1;
                    }
                    /deep/ .u-field {
                        padding: 0;
                        .u-field-inner {
                            border: 2rpx solid #e5e5e5;
                            padding: 8rpx;
                            border-radius: 10rpx;
                        }
                        .u-field__input-wrap {
                            text-align: center;
                            font-size: 24rpx;
                        }
                    }
                }
                .disabledSelect {
                    background-color: #f2f3f3;
                }
            }
            &:nth-of-type(4),
            &:last-of-type {
                /deep/ .u-field {
                    padding: 0;
                    .u-field-inner {
                        padding: 10rpx;
                        background-color: #f3f5f7;
                        border-radius: 10rpx;
                    }
                }
            }
        }
        > footer {
            display: flex;
            margin: 60rpx 0 40rpx;
            > div {
                flex: 1;
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 14rpx 0;
                background-color: #f3f5f7;
                border-radius: 8rpx;
                color: #333;
                &:last-of-type {
                    color: #fff;
                    background-color: #004ea9;
                }
            }
            > p {
                width: 50rpx;
            }
        }
    }
}
</style>
