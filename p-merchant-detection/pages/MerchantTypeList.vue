<template>
    <div id="merchantTypeList">
        <main>
            <header>
                <span>商户名称</span>
                <span>商户编号</span>
            </header>
            <section v-for="(i,key) in list" :key="key" @click="toTransDetail(i.merchantId)">
                <span>{{i.merchantName}}</span>
                <span>{{i.merchantId}}</span>
                <image src="../../static/images/home/<USER>" alt="" />
            </section>
            <p v-if="showTip">暂未查询到相关内容</p>
        </main>
    </div>
</template>

<script>
import { getMerchListBySelType } from '../../http/api'
export default {
    data() {
        return {
            merchTypeMap: ['优质商户', '活跃商户', '预流失商户', '沉睡商户'],
            list: [],
            showTip: false,
        };
    },
    onLoad() {
        this.getList()
    },
    onReady() {
        uni.setNavigationBarTitle({ title: this.merchTypeMap[this.$Route.query.merch_type] + '列表' });
    },
    methods: {
        async getList() {
            const selType = this.$Route.query.merch_type
            const { data } = await getMerchListBySelType(selType)
            this.list = data || []
            this.showTip = !this.list.length
        },
        toTransDetail(merchantId) {
            this.$Router.push({ name: 'MerchantTransDetail', params: { merchantId } })
        }
    }
};
</script>

<style lang="less" scoped>
#merchantTypeList {
    > main {
        min-height: 100vh;
        background-color: #fff;
        > header,
        > section {
            display: flex;
            > span {
                width: 50%;
                text-align: center;
                word-wrap: break-word;
            }
        }

        > header {
            padding: 24rpx;
            font-weight: bolder;
            box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.1);
        }
        > section {
            position: relative;
            padding: 30rpx 28rpx 30rpx 24rpx;
            &:nth-child(even) {
                background-color: #f3f5f7;
            }
            > image {
                position: absolute;
                right: 8rpx;
                top: 50%;
                margin-top: -8rpx;
                width: 16rpx;
                height: 16rpx;
            }
        }
        > p {
            margin: 40rpx 0;
            text-align: center;
            color: grey;
        }
    }
}
</style>
