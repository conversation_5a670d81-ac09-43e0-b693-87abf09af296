// 首页菜单标题样式
.title {
    padding: 30rpx;
    > p {
        margin: 0 0 30rpx;
        position: relative;
        > image {
            width: 8rpx;
            height: 28rpx;
            margin-right: 20rpx;
            position: relative;
            top: 2rpx;
        }
        > span {
            &:nth-of-type(1) {
                font-size: 30rpx;
                color: #222222;
                font-weight: 600;
            }
            &:nth-of-type(2) {
                font-size: 28rpx;
                color: #666666;
                position: absolute;
                top: 4rpx;
                right: 0;
            }
        }
    }
}

// 首页底部底线样式
.end-line {
	line-height: 64rpx;
	text-align: center;
	font-size: 24rpx;
	color: #999999;
	margin: 0;
}