/* ==================== 响应式样式系统 ==================== */

/* 断点定义 */
$breakpoint-xs: 0;
$breakpoint-sm: 576rpx;
$breakpoint-md: 768rpx;
$breakpoint-lg: 992rpx;
$breakpoint-xl: 1200rpx;
$breakpoint-xxl: 1600rpx;

/* ==================== 响应式混入 ==================== */

/* 媒体查询混入 */
@mixin respond-to($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: #{$breakpoint-sm - 1}) {
      @content;
    }
  }
  @if $breakpoint == sm {
    @media (min-width: #{$breakpoint-sm}) and (max-width: #{$breakpoint-md - 1}) {
      @content;
    }
  }
  @if $breakpoint == md {
    @media (min-width: #{$breakpoint-md}) and (max-width: #{$breakpoint-lg - 1}) {
      @content;
    }
  }
  @if $breakpoint == lg {
    @media (min-width: #{$breakpoint-lg}) and (max-width: #{$breakpoint-xl - 1}) {
      @content;
    }
  }
  @if $breakpoint == xl {
    @media (min-width: #{$breakpoint-xl}) and (max-width: #{$breakpoint-xxl - 1}) {
      @content;
    }
  }
  @if $breakpoint == xxl {
    @media (min-width: #{$breakpoint-xxl}) {
      @content;
    }
  }
}

/* 最小宽度媒体查询 */
@mixin min-width($breakpoint) {
  @media (min-width: #{$breakpoint}) {
    @content;
  }
}

/* 最大宽度媒体查询 */
@mixin max-width($breakpoint) {
  @media (max-width: #{$breakpoint - 1}) {
    @content;
  }
}

/* ==================== 平台适配混入 ==================== */

/* 微信小程序 */
@mixin mp-weixin {
  /* #ifdef MP-WEIXIN */
  @content;
  /* #endif */
}

/* APP */
@mixin app-plus {
  /* #ifdef APP-PLUS */
  @content;
  /* #endif */
}

/* H5 */
@mixin h5 {
  /* #ifdef H5 */
  @content;
  /* #endif */
}

/* iOS */
@mixin ios {
  /* #ifdef APP-PLUS */
  @content;
  /* #endif */
}

/* Android */
@mixin android {
  /* #ifdef APP-PLUS */
  @content;
  /* #endif */
}

/* ==================== 安全区域适配 ==================== */

/* 安全区域混入 */
@mixin safe-area($position: all) {
  @if $position == all {
    padding-top: constant(safe-area-inset-top);
    padding-top: env(safe-area-inset-top);
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: constant(safe-area-inset-left);
    padding-left: env(safe-area-inset-left);
    padding-right: constant(safe-area-inset-right);
    padding-right: env(safe-area-inset-right);
  }
  @if $position == top {
    padding-top: constant(safe-area-inset-top);
    padding-top: env(safe-area-inset-top);
  }
  @if $position == bottom {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }
  @if $position == left {
    padding-left: constant(safe-area-inset-left);
    padding-left: env(safe-area-inset-left);
  }
  @if $position == right {
    padding-right: constant(safe-area-inset-right);
    padding-right: env(safe-area-inset-right);
  }
}

/* ==================== 响应式网格系统 ==================== */

/* 容器 */
.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 $spacing-lg;
  
  @include respond-to(sm) {
    max-width: 540rpx;
  }
  
  @include respond-to(md) {
    max-width: 720rpx;
  }
  
  @include respond-to(lg) {
    max-width: 960rpx;
  }
  
  @include respond-to(xl) {
    max-width: 1140rpx;
  }
  
  @include respond-to(xxl) {
    max-width: 1320rpx;
  }
}

/* 流式容器 */
.container-fluid {
  width: 100%;
  padding: 0 $spacing-lg;
}

/* 行 */
.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 (-$spacing-sm);
}

/* 列 */
.col {
  flex: 1;
  padding: 0 $spacing-sm;
}

/* 响应式列 */
@for $i from 1 through 12 {
  .col-#{$i} {
    flex: 0 0 percentage($i / 12);
    max-width: percentage($i / 12);
    padding: 0 $spacing-sm;
  }
  
  .col-xs-#{$i} {
    @include max-width($breakpoint-sm) {
      flex: 0 0 percentage($i / 12);
      max-width: percentage($i / 12);
    }
  }
  
  .col-sm-#{$i} {
    @include min-width($breakpoint-sm) {
      flex: 0 0 percentage($i / 12);
      max-width: percentage($i / 12);
    }
  }
  
  .col-md-#{$i} {
    @include min-width($breakpoint-md) {
      flex: 0 0 percentage($i / 12);
      max-width: percentage($i / 12);
    }
  }
  
  .col-lg-#{$i} {
    @include min-width($breakpoint-lg) {
      flex: 0 0 percentage($i / 12);
      max-width: percentage($i / 12);
    }
  }
  
  .col-xl-#{$i} {
    @include min-width($breakpoint-xl) {
      flex: 0 0 percentage($i / 12);
      max-width: percentage($i / 12);
    }
  }
}

/* ==================== 响应式显示/隐藏 ==================== */

/* 显示类 */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }

/* 响应式显示 */
@include max-width($breakpoint-sm) {
  .d-xs-none { display: none !important; }
  .d-xs-block { display: block !important; }
  .d-xs-flex { display: flex !important; }
  .d-xs-inline { display: inline !important; }
  .d-xs-inline-block { display: inline-block !important; }
}

@include min-width($breakpoint-sm) {
  .d-sm-none { display: none !important; }
  .d-sm-block { display: block !important; }
  .d-sm-flex { display: flex !important; }
  .d-sm-inline { display: inline !important; }
  .d-sm-inline-block { display: inline-block !important; }
}

@include min-width($breakpoint-md) {
  .d-md-none { display: none !important; }
  .d-md-block { display: block !important; }
  .d-md-flex { display: flex !important; }
  .d-md-inline { display: inline !important; }
  .d-md-inline-block { display: inline-block !important; }
}

@include min-width($breakpoint-lg) {
  .d-lg-none { display: none !important; }
  .d-lg-block { display: block !important; }
  .d-lg-flex { display: flex !important; }
  .d-lg-inline { display: inline !important; }
  .d-lg-inline-block { display: inline-block !important; }
}

@include min-width($breakpoint-xl) {
  .d-xl-none { display: none !important; }
  .d-xl-block { display: block !important; }
  .d-xl-flex { display: flex !important; }
  .d-xl-inline { display: inline !important; }
  .d-xl-inline-block { display: inline-block !important; }
}

/* ==================== 响应式间距 ==================== */

/* 响应式内边距 */
@for $i from 0 through 5 {
  .p-#{$i} { padding: #{$i * $spacing-xs} !important; }
  .pt-#{$i} { padding-top: #{$i * $spacing-xs} !important; }
  .pb-#{$i} { padding-bottom: #{$i * $spacing-xs} !important; }
  .pl-#{$i} { padding-left: #{$i * $spacing-xs} !important; }
  .pr-#{$i} { padding-right: #{$i * $spacing-xs} !important; }
  .px-#{$i} { 
    padding-left: #{$i * $spacing-xs} !important;
    padding-right: #{$i * $spacing-xs} !important;
  }
  .py-#{$i} { 
    padding-top: #{$i * $spacing-xs} !important;
    padding-bottom: #{$i * $spacing-xs} !important;
  }
  
  .m-#{$i} { margin: #{$i * $spacing-xs} !important; }
  .mt-#{$i} { margin-top: #{$i * $spacing-xs} !important; }
  .mb-#{$i} { margin-bottom: #{$i * $spacing-xs} !important; }
  .ml-#{$i} { margin-left: #{$i * $spacing-xs} !important; }
  .mr-#{$i} { margin-right: #{$i * $spacing-xs} !important; }
  .mx-#{$i} { 
    margin-left: #{$i * $spacing-xs} !important;
    margin-right: #{$i * $spacing-xs} !important;
  }
  .my-#{$i} { 
    margin-top: #{$i * $spacing-xs} !important;
    margin-bottom: #{$i * $spacing-xs} !important;
  }
}

/* ==================== 响应式字体 ==================== */

/* 响应式字体大小 */
.text-responsive {
  font-size: $font-size-base;
  
  @include respond-to(xs) {
    font-size: $font-size-sm;
  }
  
  @include respond-to(lg) {
    font-size: $font-size-lg;
  }
}

/* ==================== 平台特定样式 ==================== */

/* 微信小程序特定样式 */
@include mp-weixin {
  .mp-only {
    display: block;
  }
  
  /* 微信小程序状态栏适配 */
  .status-bar {
    height: var(--status-bar-height);
  }
}

/* APP特定样式 */
@include app-plus {
  .app-only {
    display: block;
  }
  
  /* APP安全区域适配 */
  .safe-area-top {
    @include safe-area(top);
  }
  
  .safe-area-bottom {
    @include safe-area(bottom);
  }
}

/* H5特定样式 */
@include h5 {
  .h5-only {
    display: block;
  }
}

/* 默认隐藏平台特定元素 */
.mp-only,
.app-only,
.h5-only {
  display: none;
}
