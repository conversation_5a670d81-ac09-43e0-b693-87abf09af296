/* ==================== 现代化全局样式 ==================== */

page {
  width: 100%;
  height: 100%;
  color: $text-primary;
  font-size: $font-size-base;
  font-weight: $font-weight-normal;
  line-height: $line-height-normal;
  background-color: $bg-secondary;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

view,
div {
  box-sizing: border-box;
}

/* ==================== 布局组件 ==================== */

// 现代化间隔样式
.interval {
  height: $spacing-md;
  background-color: $bg-secondary;
  margin: 0;
}

.interval-sm {
  height: $spacing-sm;
  background-color: $bg-secondary;
  margin: 0;
}

.interval-lg {
  height: $spacing-lg;
  background-color: $bg-secondary;
  margin: 0;
}

/* ==================== 按钮系统 ==================== */

// 主要按钮
.btn-primary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 160rpx;
  height: 88rpx;
  padding: 0 $spacing-lg;
  background: $gradient-primary;
  color: $bg-primary;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  border: none;
  border-radius: $radius-medium;
  box-shadow: $shadow-medium;
  transition: all 0.3s ease;

  &:active {
    transform: translateY(2rpx);
    box-shadow: $shadow-light;
  }

  &:disabled {
    opacity: 0.6;
    transform: none;
    box-shadow: none;
  }
}

// 次要按钮
.btn-secondary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 160rpx;
  height: 88rpx;
  padding: 0 $spacing-lg;
  background: $bg-primary;
  color: $primary-color;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  border: 2rpx solid $primary-color;
  border-radius: $radius-medium;
  transition: all 0.3s ease;

  &:active {
    background: $primary-color;
    color: $bg-primary;
  }
}

// 文本按钮
.btn-text {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 120rpx;
  height: 64rpx;
  padding: 0 $spacing-md;
  background: transparent;
  color: $primary-color;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  border: none;
  border-radius: $radius-small;
  transition: all 0.3s ease;

  &:active {
    background: rgba(26, 115, 232, 0.08);
  }
}

// 通用按钮容器
.custom-button {
  width: 100%;
  padding: $spacing-md $spacing-lg;
  text-align: center;
  background-color: $bg-secondary;

  > button {
    width: 100%;
    max-width: 560rpx;
    margin: 0 auto;
  }
}
/* ==================== 卡片系统 ==================== */

.card {
  background: $bg-primary;
  border-radius: $radius-medium;
  box-shadow: $shadow-light;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: $shadow-medium;
  }
}

.card-header {
  padding: $spacing-lg;
  border-bottom: 1rpx solid $border-light;

  .card-title {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-primary;
    margin: 0;
  }

  .card-subtitle {
    font-size: $font-size-sm;
    color: $text-secondary;
    margin: $spacing-xs 0 0 0;
  }
}

.card-body {
  padding: $spacing-lg;
}

.card-footer {
  padding: $spacing-lg;
  border-top: 1rpx solid $border-light;
  background: $bg-tertiary;
}

/* ==================== 搜索栏 ==================== */

.search-bar {
  display: flex;
  justify-content: center;
  padding: $spacing-lg;
  background-color: $bg-secondary;

  .search-content {
    width: 100%;
    max-width: 640rpx;
    position: relative;

    .search-input {
      width: 100%;
      height: 88rpx;
      padding: 0 $spacing-lg 0 96rpx;
      background: $bg-primary;
      border: 2rpx solid $border-light;
      border-radius: $radius-large;
      font-size: $font-size-base;
      color: $text-primary;
      transition: all 0.3s ease;

      &:focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 6rpx rgba(26, 115, 232, 0.1);
      }

      &::placeholder {
        color: $text-tertiary;
      }
    }

    .search-icon {
      position: absolute;
      left: $spacing-lg;
      top: 50%;
      transform: translateY(-50%);
      color: $text-tertiary;
      font-size: 32rpx;
    }
  }
}

/* ==================== 列表项 ==================== */

.list-item {
  display: flex;
  align-items: center;
  padding: $spacing-lg;
  background: $bg-primary;
  border-bottom: 1rpx solid $border-light;
  transition: all 0.3s ease;

  &:active {
    background: $bg-tertiary;
  }

  &:last-child {
    border-bottom: none;
  }

  .item-icon {
    width: 80rpx;
    height: 80rpx;
    margin-right: $spacing-md;
    border-radius: $radius-medium;
    background: $bg-tertiary;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .item-content {
    flex: 1;

    .item-title {
      font-size: $font-size-base;
      font-weight: $font-weight-medium;
      color: $text-primary;
      margin: 0 0 $spacing-xs 0;
    }

    .item-subtitle {
      font-size: $font-size-sm;
      color: $text-secondary;
      margin: 0;
    }
  }

  .item-action {
    margin-left: $spacing-md;
    color: $text-tertiary;
  }
}

/* ==================== 状态样式 ==================== */

.status-success {
  color: $secondary-color;
  background: rgba(52, 168, 83, 0.1);
  padding: $spacing-xs $spacing-sm;
  border-radius: $radius-small;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
}

.status-warning {
  color: $warning-color;
  background: rgba(251, 188, 4, 0.1);
  padding: $spacing-xs $spacing-sm;
  border-radius: $radius-small;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
}

.status-error {
  color: $error-color;
  background: rgba(234, 67, 53, 0.1);
  padding: $spacing-xs $spacing-sm;
  border-radius: $radius-small;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
}

/* ==================== 弹窗优化 ==================== */

.news-popup {
  /deep/ .u-mode-center-box {
    overflow: visible !important;
    border-radius: $radius-large;

    .u-close--bottom-left {
      bottom: -90rpx !important;
      left: 50% !important;
      transform: translateX(-50%);
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10rpx);
      border-radius: 50%;
      width: 64rpx;
      height: 64rpx;
    }
  }
}

/* ==================== 工具类 ==================== */

// 文本对齐
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

// 文本颜色
.text-primary { color: $text-primary; }
.text-secondary { color: $text-secondary; }
.text-tertiary { color: $text-tertiary; }
.text-success { color: $secondary-color; }
.text-warning { color: $warning-color; }
.text-error { color: $error-color; }

// 字体大小
.text-xs { font-size: $font-size-xs; }
.text-sm { font-size: $font-size-sm; }
.text-base { font-size: $font-size-base; }
.text-lg { font-size: $font-size-lg; }
.text-xl { font-size: $font-size-xl; }
.text-xxl { font-size: $font-size-xxl; }

// 字体权重
.font-light { font-weight: $font-weight-light; }
.font-normal { font-weight: $font-weight-normal; }
.font-medium { font-weight: $font-weight-medium; }
.font-semibold { font-weight: $font-weight-semibold; }
.font-bold { font-weight: $font-weight-bold; }

// 边距
.m-0 { margin: 0; }
.m-xs { margin: $spacing-xs; }
.m-sm { margin: $spacing-sm; }
.m-md { margin: $spacing-md; }
.m-lg { margin: $spacing-lg; }
.m-xl { margin: $spacing-xl; }

.mt-0 { margin-top: 0; }
.mt-xs { margin-top: $spacing-xs; }
.mt-sm { margin-top: $spacing-sm; }
.mt-md { margin-top: $spacing-md; }
.mt-lg { margin-top: $spacing-lg; }
.mt-xl { margin-top: $spacing-xl; }

.mb-0 { margin-bottom: 0; }
.mb-xs { margin-bottom: $spacing-xs; }
.mb-sm { margin-bottom: $spacing-sm; }
.mb-md { margin-bottom: $spacing-md; }
.mb-lg { margin-bottom: $spacing-lg; }
.mb-xl { margin-bottom: $spacing-xl; }

.ml-0 { margin-left: 0; }
.ml-xs { margin-left: $spacing-xs; }
.ml-sm { margin-left: $spacing-sm; }
.ml-md { margin-left: $spacing-md; }
.ml-lg { margin-left: $spacing-lg; }
.ml-xl { margin-left: $spacing-xl; }

.mr-0 { margin-right: 0; }
.mr-xs { margin-right: $spacing-xs; }
.mr-sm { margin-right: $spacing-sm; }
.mr-md { margin-right: $spacing-md; }
.mr-lg { margin-right: $spacing-lg; }
.mr-xl { margin-right: $spacing-xl; }

// 内边距
.p-0 { padding: 0; }
.p-xs { padding: $spacing-xs; }
.p-sm { padding: $spacing-sm; }
.p-md { padding: $spacing-md; }
.p-lg { padding: $spacing-lg; }
.p-xl { padding: $spacing-xl; }

.pt-0 { padding-top: 0; }
.pt-xs { padding-top: $spacing-xs; }
.pt-sm { padding-top: $spacing-sm; }
.pt-md { padding-top: $spacing-md; }
.pt-lg { padding-top: $spacing-lg; }
.pt-xl { padding-top: $spacing-xl; }

.pb-0 { padding-bottom: 0; }
.pb-xs { padding-bottom: $spacing-xs; }
.pb-sm { padding-bottom: $spacing-sm; }
.pb-md { padding-bottom: $spacing-md; }
.pb-lg { padding-bottom: $spacing-lg; }
.pb-xl { padding-bottom: $spacing-xl; }

.pl-0 { padding-left: 0; }
.pl-xs { padding-left: $spacing-xs; }
.pl-sm { padding-left: $spacing-sm; }
.pl-md { padding-left: $spacing-md; }
.pl-lg { padding-left: $spacing-lg; }
.pl-xl { padding-left: $spacing-xl; }

.pr-0 { padding-right: 0; }
.pr-xs { padding-right: $spacing-xs; }
.pr-sm { padding-right: $spacing-sm; }
.pr-md { padding-right: $spacing-md; }
.pr-lg { padding-right: $spacing-lg; }
.pr-xl { padding-right: $spacing-xl; }

// Flex布局
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }
.flex-1 { flex: 1; }
.flex-auto { flex: auto; }
.flex-none { flex: none; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

// 圆角
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: $radius-small; }
.rounded { border-radius: $radius-medium; }
.rounded-lg { border-radius: $radius-large; }
.rounded-xl { border-radius: $radius-xl; }
.rounded-full { border-radius: 50%; }

// 阴影
.shadow-none { box-shadow: none; }
.shadow-light { box-shadow: $shadow-light; }
.shadow-medium { box-shadow: $shadow-medium; }
.shadow-heavy { box-shadow: $shadow-heavy; }

// 透明度
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* ==================== 响应式优化 ==================== */

// 安全区域适配
.safe-area-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

// 加载更多优化
/deep/ .u-load-more-wrap {
  padding: $spacing-lg 0;
  color: $text-tertiary;
  font-size: $font-size-sm;
}