page {
  width: 100%;
  height: 100%;
  color: $u-content-color;
  font-size: 28rpx;
  background-color: #f3f5f7;
}
view,
div {
  box-sizing: border-box;
}

// 间隔样式
.interval {
  height: 20rpx;
  background-color: #f3f5f7;
  margin: 0;
}

/deep/ .u-load-more-wrap {
  padding: 20rpx 0;
}

// 通用按钮样式
.custom-button {
  width: 100%;
  height: 100rpx;
  padding: 10rpx 0;
  text-align: center;
  background-color: #f3f5f7;
  z-index: 3;
  > button {
    width: 560rpx;
    height: 80rpx;
    line-height: 80rpx;
    background-color: #004ea9;
    color: white;
    font-size: 30rpx;
    border: none;
    border-radius: 10rpx;
  }
}
.search-bar {
  display: flex;
  justify-content: center;
  height: 110rpx;
  padding: 20rpx 50rpx;
  background-color: #f3f5f7;
  // border-top: 1rpx solid #e5e5e5;
  .content {
    width: 100%;
    border-radius: 40rpx;
    background-color: #fff;
    /deep/ .u-field {
      display: flex;
      align-items: center;
      height: 100%;
      padding: 0 28rpx;
      .u-field-inner {
        width: 100%;
      }
    }
  }
}

.news-popup {
  /deep/ .u-mode-center-box {
       overflow: visible !important;
       .u-close--bottom-left {
           bottom: -90rpx !important;
           left: 50% !important;
           transform: translateX(-50%);
       }
   }
}