// 下拉菜单栏
.filter-header {
  position: relative;
  height: 80rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f3f5f7;
  /deep/.u-dropdown__menu {
    padding: 0 30rpx;
  }
  
  /deep/ .filter-form {
    background-color: #fff;
    .u-label {
      flex: 0 0 160rpx !important;
    }

    .u-size-medium{
        padding: 0 60rpx;
        height: 66rpx;
    }

    .u-radio-group {
      padding: 20rpx 28rpx;
    }
  }

  .btnTools {
    display: flex;
    justify-content: space-around;
    padding: 24rpx 0;
  }
}

// 筛选时间
.filtrate-time {
  display: flex;
  align-items: center;
  /deep/ .u-label {
    display: none !important;
  }
  /deep/ .u-field__input-wrap {
    text-align: center;
  }
}
