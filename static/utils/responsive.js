/**
 * 响应式工具库
 * 处理多端适配和响应式布局
 */

/**
 * 设备类型枚举
 */
export const DEVICE_TYPE = {
  MOBILE: 'mobile',
  TABLET: 'tablet',
  DESKTOP: 'desktop'
}

/**
 * 平台类型枚举
 */
export const PLATFORM_TYPE = {
  WECHAT: 'mp-weixin',
  ALIPAY: 'mp-alipay',
  BAIDU: 'mp-baidu',
  TOUTIAO: 'mp-toutiao',
  QQ: 'mp-qq',
  APP_PLUS: 'app-plus',
  H5: 'h5'
}

/**
 * 屏幕尺寸断点
 */
export const BREAKPOINTS = {
  xs: 0,      // 超小屏幕
  sm: 576,    // 小屏幕
  md: 768,    // 中等屏幕
  lg: 992,    // 大屏幕
  xl: 1200,   // 超大屏幕
  xxl: 1600   // 超超大屏幕
}

/**
 * 响应式管理器
 */
class ResponsiveManager {
  constructor() {
    this.systemInfo = null
    this.screenInfo = null
    this.safeArea = null
    this.init()
  }
  
  /**
   * 初始化
   */
  init() {
    try {
      this.systemInfo = uni.getSystemInfoSync()
      this.calculateScreenInfo()
      this.calculateSafeArea()
    } catch (error) {
      console.error('ResponsiveManager init failed:', error)
    }
  }
  
  /**
   * 计算屏幕信息
   */
  calculateScreenInfo() {
    if (!this.systemInfo) return
    
    const { screenWidth, screenHeight, pixelRatio } = this.systemInfo
    
    this.screenInfo = {
      width: screenWidth,
      height: screenHeight,
      pixelRatio,
      // rpx转px的比例
      rpxRatio: screenWidth / 750,
      // 设备类型
      deviceType: this.getDeviceType(),
      // 屏幕方向
      orientation: screenWidth > screenHeight ? 'landscape' : 'portrait'
    }
  }
  
  /**
   * 计算安全区域
   */
  calculateSafeArea() {
    if (!this.systemInfo) return
    
    const { 
      statusBarHeight = 0, 
      safeArea = {}, 
      screenWidth, 
      screenHeight 
    } = this.systemInfo
    
    this.safeArea = {
      top: statusBarHeight,
      bottom: screenHeight - (safeArea.bottom || screenHeight),
      left: safeArea.left || 0,
      right: screenWidth - (safeArea.right || screenWidth)
    }
  }
  
  /**
   * 获取设备类型
   * @returns {string} 设备类型
   */
  getDeviceType() {
    if (!this.systemInfo) return DEVICE_TYPE.MOBILE
    
    const { screenWidth } = this.systemInfo
    
    if (screenWidth >= BREAKPOINTS.lg) {
      return DEVICE_TYPE.DESKTOP
    } else if (screenWidth >= BREAKPOINTS.md) {
      return DEVICE_TYPE.TABLET
    } else {
      return DEVICE_TYPE.MOBILE
    }
  }
  
  /**
   * 获取当前平台
   * @returns {string} 平台类型
   */
  getPlatform() {
    // #ifdef MP-WEIXIN
    return PLATFORM_TYPE.WECHAT
    // #endif
    
    // #ifdef MP-ALIPAY
    return PLATFORM_TYPE.ALIPAY
    // #endif
    
    // #ifdef MP-BAIDU
    return PLATFORM_TYPE.BAIDU
    // #endif
    
    // #ifdef MP-TOUTIAO
    return PLATFORM_TYPE.TOUTIAO
    // #endif
    
    // #ifdef MP-QQ
    return PLATFORM_TYPE.QQ
    // #endif
    
    // #ifdef APP-PLUS
    return PLATFORM_TYPE.APP_PLUS
    // #endif
    
    // #ifdef H5
    return PLATFORM_TYPE.H5
    // #endif
    
    return 'unknown'
  }
  
  /**
   * rpx转px
   * @param {number} rpx rpx值
   * @returns {number} px值
   */
  rpxToPx(rpx) {
    if (!this.screenInfo) return rpx
    return rpx * this.screenInfo.rpxRatio
  }
  
  /**
   * px转rpx
   * @param {number} px px值
   * @returns {number} rpx值
   */
  pxToRpx(px) {
    if (!this.screenInfo) return px
    return px / this.screenInfo.rpxRatio
  }
  
  /**
   * 获取响应式值
   * @param {object} values 不同断点的值
   * @returns {any} 当前断点对应的值
   */
  getResponsiveValue(values) {
    if (!this.screenInfo) return values.xs || values.default
    
    const { width } = this.screenInfo
    
    if (width >= BREAKPOINTS.xxl && values.xxl !== undefined) return values.xxl
    if (width >= BREAKPOINTS.xl && values.xl !== undefined) return values.xl
    if (width >= BREAKPOINTS.lg && values.lg !== undefined) return values.lg
    if (width >= BREAKPOINTS.md && values.md !== undefined) return values.md
    if (width >= BREAKPOINTS.sm && values.sm !== undefined) return values.sm
    
    return values.xs || values.default
  }
  
  /**
   * 获取安全区域样式
   * @param {string} position 位置 top|bottom|left|right
   * @returns {string} 样式值
   */
  getSafeAreaStyle(position) {
    if (!this.safeArea) return '0px'
    
    const value = this.safeArea[position] || 0
    return `${value}px`
  }
  
  /**
   * 获取导航栏高度
   * @returns {number} 导航栏高度(px)
   */
  getNavBarHeight() {
    if (!this.systemInfo) return 44
    
    const { platform, statusBarHeight = 0 } = this.systemInfo
    const navBarHeight = platform === 'ios' ? 44 : 48
    
    return statusBarHeight + navBarHeight
  }
  
  /**
   * 获取TabBar高度
   * @returns {number} TabBar高度(px)
   */
  getTabBarHeight() {
    // 不同平台TabBar高度可能不同
    const platform = this.getPlatform()
    
    switch (platform) {
      case PLATFORM_TYPE.WECHAT:
        return 50
      case PLATFORM_TYPE.APP_PLUS:
        return this.systemInfo?.platform === 'ios' ? 83 : 56
      default:
        return 50
    }
  }
  
  /**
   * 检查是否为小屏设备
   * @returns {boolean}
   */
  isSmallScreen() {
    return this.getDeviceType() === DEVICE_TYPE.MOBILE
  }
  
  /**
   * 检查是否为平板设备
   * @returns {boolean}
   */
  isTablet() {
    return this.getDeviceType() === DEVICE_TYPE.TABLET
  }
  
  /**
   * 检查是否为桌面设备
   * @returns {boolean}
   */
  isDesktop() {
    return this.getDeviceType() === DEVICE_TYPE.DESKTOP
  }
  
  /**
   * 检查是否为横屏
   * @returns {boolean}
   */
  isLandscape() {
    return this.screenInfo?.orientation === 'landscape'
  }
  
  /**
   * 检查是否为竖屏
   * @returns {boolean}
   */
  isPortrait() {
    return this.screenInfo?.orientation === 'portrait'
  }
  
  /**
   * 获取适配后的字体大小
   * @param {number} baseFontSize 基础字体大小(rpx)
   * @returns {number} 适配后的字体大小(rpx)
   */
  getAdaptiveFontSize(baseFontSize) {
    if (!this.screenInfo) return baseFontSize
    
    const { width } = this.screenInfo
    const scale = width / 375 // 以iPhone 6/7/8为基准
    
    // 限制缩放范围
    const minScale = 0.8
    const maxScale = 1.2
    const finalScale = Math.max(minScale, Math.min(maxScale, scale))
    
    return Math.round(baseFontSize * finalScale)
  }
  
  /**
   * 获取网格列数
   * @param {object} columns 不同断点的列数配置
   * @returns {number} 当前断点的列数
   */
  getGridColumns(columns = { xs: 1, sm: 2, md: 3, lg: 4, xl: 5 }) {
    return this.getResponsiveValue(columns)
  }
  
  /**
   * 获取容器内边距
   * @returns {string} 内边距值(rpx)
   */
  getContainerPadding() {
    const padding = this.getResponsiveValue({
      xs: 32,
      sm: 40,
      md: 48,
      lg: 64,
      xl: 80
    })
    
    return `${padding}rpx`
  }
  
  /**
   * 获取卡片间距
   * @returns {string} 间距值(rpx)
   */
  getCardSpacing() {
    const spacing = this.getResponsiveValue({
      xs: 16,
      sm: 20,
      md: 24,
      lg: 32,
      xl: 40
    })
    
    return `${spacing}rpx`
  }
}

// 创建全局实例
const responsive = new ResponsiveManager()

/**
 * 响应式混入
 */
export const responsiveMixin = {
  data() {
    return {
      $responsive: responsive
    }
  },
  
  computed: {
    $deviceType() {
      return responsive.getDeviceType()
    },
    
    $platform() {
      return responsive.getPlatform()
    },
    
    $isSmallScreen() {
      return responsive.isSmallScreen()
    },
    
    $isTablet() {
      return responsive.isTablet()
    },
    
    $isDesktop() {
      return responsive.isDesktop()
    },
    
    $isLandscape() {
      return responsive.isLandscape()
    },
    
    $isPortrait() {
      return responsive.isPortrait()
    },
    
    $safeAreaTop() {
      return responsive.getSafeAreaStyle('top')
    },
    
    $safeAreaBottom() {
      return responsive.getSafeAreaStyle('bottom')
    },
    
    $navBarHeight() {
      return responsive.getNavBarHeight() + 'px'
    },
    
    $tabBarHeight() {
      return responsive.getTabBarHeight() + 'px'
    }
  },
  
  methods: {
    $rpxToPx(rpx) {
      return responsive.rpxToPx(rpx)
    },
    
    $pxToRpx(px) {
      return responsive.pxToRpx(px)
    },
    
    $getResponsiveValue(values) {
      return responsive.getResponsiveValue(values)
    },
    
    $getAdaptiveFontSize(baseFontSize) {
      return responsive.getAdaptiveFontSize(baseFontSize)
    },
    
    $getGridColumns(columns) {
      return responsive.getGridColumns(columns)
    }
  }
}

export default responsive
