import { KJ<PERSON>, hextob64 } from 'jsrsasign'

// 私钥 pem格式
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
//        `
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    `

/**
 * 签名
 * @param params 需要签名的参数
 * @param isSpliceParames 是否是拼接在路径后的形式传参 形如path?key=value, 如果是数字要将其转为字符串和java保持一致
 * @return string 返回签名结果
 */
export function generateSign(params, isSpliceParames) {
  // 参数为空不处理
  if (!params) return ''
  try {
    let sign_params = {}
    // 只有一项参数无需排序
    if (Object.keys(params).length === 1) {
      sign_params = params
    } else {
      // 签名参数排序
      Object.keys(params)
        .sort()
        .forEach(key => {
          sign_params[key] = params[key]
        })
    }
    // 参数转json
    const sign_params2json = JSON.stringify(sign_params, function (key, value) {
      // 不参与签名
      if (!key) {
        Object.keys(value).forEach(out_key => {
          if ([null, ''].includes(value[out_key])) value[out_key] = undefined
        })
      }
      // 如path?key=value格式, value === number ? string : 原格式
      if (isSpliceParames && Number.isFinite(value)) return String(value)
       // 排除数组
      if (key && Object.prototype.toString.call(value) === '[object Array]') return undefined
      return value
    })
    console.log('签名参数=>>>>>>', sign_params2json)
    const signature = new KJUR.crypto.Signature({ alg: 'MD5withRSA', prvkeypem: PRIVATE_KEY })
    signature.updateString(sign_params2json)
    // #ifdef APP-IOS
    uni.showLoading({ title: '加载中', mask: true })
    // #endif
    const signatureHex = signature.sign() // 16进制格式签名
    return hextob64(signatureHex) // base64格式签名
  } catch (error) {
    console.error(error)
  }
}
