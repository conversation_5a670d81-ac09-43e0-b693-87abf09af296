<template>
    <div id="orderManagement">
        <main>
            <u-cell-group>
                <u-cell-item title="分润明细" @click="toLink('ShareProfitDetail')" />
                <u-cell-item title="商户提现手续费订单" @click="toLink('MerchantWithdrawalFee')" />
            </u-cell-group>
        </main>
    </div>
</template>

<script>
export default {
    name: "OrderManagement",
    data() {
        return {

        }
    },
    methods: {
        toLink(name) {
            this.$Router.push({ name });
        }
    },
}
</script>

<style lang="less" scoped>
#orderManagement {
    padding-top: 20rpx;
}
</style>