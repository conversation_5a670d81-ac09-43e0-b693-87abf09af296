<template>
    <div>
        <main>
            <no-content v-show="show" />

            <section v-for="(i,index) in  list" :key="index">
                <p>
                    <span>代理商名称:</span>
                    <span>{{i.agentName}}</span>
                </p>
                <p v-if="agentLevel == 1">
                    <span>代理商等级:</span>
                    <span>{{i.agentLevel}}</span>
                </p>
                <p>
                    <span>返现金额:</span>
                    <span>{{i.originTradeAmount}} 元</span>
                </p>
                <p>
                    <span>入账金额:</span>
                    <span>{{i.tradeAmount}} 元 <span style="font-size:12px">(扣除税点)</span></span>
                </p>
                <p>
                    <span>入账类型:</span>
                    <span>{{i.postTypeDesc}}</span>
                </p>
                <p>
                    <span>入账状态:</span>
                    <span>{{i.postStatusDesc}}</span>
                </p>
                <p>
                    <span>创建时间:</span>
                    <span>{{i.createTime ? dateFormat(i.createTime) : '--'}}</span>
                </p>
            </section>
        </main>
    </div>
</template>

<script>
import { dateFormat } from "../../static/utils/date.js";
import { getMerRateRecordDetail } from '../../http/api'

export default {
    data() {
        return {
            list: [],
            show: false,
        };
    },
    computed: {
        agentLevel() {
            return this.$store.state.userInfo.agentLevel
        }
    },
    onLoad() {
        this.getListData()
    },
    methods: {
        async getListData() {
            const { data } = await getMerRateRecordDetail(this.$Route.query.id)
            this.list = data || []
            this.show = !this.list.length
        },
        dateFormat
    }
};
</script>

<style lang="scss" scoped>
main {
    min-height: 100vh;
    padding: 20rpx 20rpx 0;
    background-color: #f3f5f7;
    > section {
        padding: 30rpx;
        margin-bottom: 20rpx;
        background-color: #fff;
        border-radius: 16rpx;
        p {
            margin: 0;
            span {
                &:first-of-type {
                    display: inline-block;
                    min-width: calc(5em + 30rpx);
                    color: #8799a3;
                }
            }
            &:not(:last-of-type) {
                margin-bottom: 10rpx;
            }
        }
    }
}
</style>
