<template>
    <div id="shareProfitDetail">
        <main>
            <u-sticky>
                <section class="filter-menu">
                    <view class="filter-time">
                        <u-field disabled :border-bottom="false" :value="startTime" placeholder="开始时间" @click="showStartPicker = true" />
                        <u-picker v-model="showStartPicker" title="开始时间" :show-time-tag="false" mode="time" @confirm="onConfirmStart" />
                        <span>—</span>
                        <u-field disabled :border-bottom="false" :value="endTime" placeholder="结束时间" @click="showEndPicker = true" />
                        <u-picker v-model="showEndPicker" title="结束时间" :show-time-tag="false" mode="time" @confirm="onConfirmEnd" />
                    </view>
                    <view class="filter-btn">
                        <text @click="getProfitList">搜索</text>
                    </view>
                </section>
            </u-sticky>

            <no-content v-if="show" />
            <view v-show="!show" class="list-data">
                <section v-for="p in profit" :key="p.index">
					  <u-tag :type="p.incomeSourceType === 1 ? 'primary' : 'success'" size="mini" :text="p.incomeSourceType === 1 ? '团队' : '直属'"/>
                    <section>
                        <p>
                            {{ p.merchantName }}
                            <span>({{ transactionTypes[p.transactionType] || '' }})</span>
                        </p>
						<p>{{ p.payOrgCode | orgCodeFormat}}</p>
                        <p>{{ p.createTime }}</p>
                    </section>
                    <p>
                        <span>{{ p.transactionAmount | toDecimal2 }}</span>
                        <span>+{{ p.amount | toDecimal2 }}</span>
                    </p>
                </section>
            </view>

            <u-loadmore v-show="!show" :status="status" @loadmore="loadmore" />
        </main>

        <u-back-top :scrollTop="scrollTop" />

    </div>
</template>

<script>
import { dateFormat, toDecimal2 } from "../../static/utils/date"
import { getProfitList } from "../../http/api"
const transactionTypes = ['借记卡', '信用卡', '云闪付', '扫码', '借记卡封顶值', '信用卡特惠', '', '信用卡秒到' , '无卡(贷记)' , '无卡(借记)','支付宝大额']

export default {
    name: "ShareProfitDetail",
    filters: {
        toDecimal2
    },
    data() {
        return {
            showStartPicker: false,
            showEndPicker: false,
            startTime: dateFormat(new Date()).substring(0, 10),
            endTime: dateFormat(new Date()).substring(0, 10),
            show: false,
            profit: [],
            status: 'loading',
            page: null,
            transactionTypes,
            scrollTop: 0
        };
    },
    onLoad() {
        this.getProfitList();
    },
    onPageScroll(e) {
        this.scrollTop = e.scrollTop;
    },
    onReachBottom() {
        this.loadmore()
    },
    methods: {
        getProfitList() {
            this.status = 'loading';
            getProfitList({
                startTime: this.startTime == "" ? null : this.startTime + " 00:00:00",
                endTime: this.endTime == "" ? null : this.endTime + " 23:59:59",
                status: 0,
                index: 0,
                size: 100,
            }).then((res) => {
                if (res.code == "00") {
                    if (res.data.list.length != 0) {
                        this.show = false;
                        this.page = res.data.count;
                        this.profit = res.data.list;
                        uni.pageScrollTo({ scrollTop: 0 });
                        if (this.profit.length >= this.page) {
                            // 数据全部加载完成
                            this.status = 'nomore';
                        } else {
                            this.status = 'loadmore';
                        }
                    } else {
                        this.show = true;
                    }
                }
            });
        },
        onConfirmStart(val) {
            this.startTime = dateFormat(val.timestamp * 1000).substring(0, 10);
            this.showStartPicker = false;
        },
        onConfirmEnd(val) {
            this.endTime = dateFormat(val.timestamp * 1000).substring(0, 10);
            this.showEndPicker = false;
        },
        loadmore() {
            if (this.status == 'nomore') return;
            this.status = 'loading'
            getProfitList({
                startTime: this.startTime == "" ? null : this.startTime + " 00:00:00",
                endTime: this.endTime == "" ? null : this.endTime + " 23:59:59",
                status: 0,
                index: this.profit.length,
                size: 100,
            }).then((res) => {
                if (res.code == "00") {
                    if (res.data.list.length != 0) {
                        this.show = false;
                        this.page = res.data.count;
                        res.data.list.forEach((i) => {
                            this.profit.push(i);
                        });
                    } else {
                        this.show = true;
                    }
                    if (this.profit.length >= this.page) {
                        // 数据全部加载完成
                        this.status = 'nomore';
                    } else {
                        this.status = 'loadmore';
                    }
                }
            });
        },
    },
};
</script>

<style lang="less" scoped>
#shareProfitDetail {
    main {
        width: 100%;
        text-align: center;
        .filter-menu {
            display: flex;
            padding: 20rpx;
            background-color: #fff;
            border-bottom: 1px solid #e5e5e5;
            /deep/ .filter-time {
                display: flex;
                align-items: center;
                background-color: #f3f5f7;
                border-radius: 50rpx;
                .u-label {
                    display: none;
                }
                > span {
                    margin: 0 16rpx;
                }
                .u-field {
                    padding: 10rpx 20rpx;
                    color: #303133;
                    border-radius: 10rpx;
                }
                .u-field__input-wrap {
                    text-align: center;
                }
            }
            .filter-btn {
                flex-shrink: 0;
                margin-left: 20rpx;
                text {
                    display: inline-block;
                    padding: 16rpx 20rpx;
                    color: #004ea9;
                }
            }
        }
        .list-data {
            > section {
                background-color: white;
                display: flex;
                align-items: center;
                justify-content: space-between;
                position: relative;
                padding: 0 20rpx;
                border-bottom: 1px solid #f3f5f7;
                > section {
                    flex: 1;
                    text-align: left;
					margin-left: 16rpx;
                    > p {
						margin: 0;
                        &:nth-of-type(1) {
                            font-size: 30rpx;
                            margin: 30rpx 30rpx 4rpx 0;
                            span {
                                color: #777676;
                            }
                        } 
						&:nth-of-type(2) {
                            margin: 0 30rpx 10rpx 0;
                           color: #555;
                        }
                        &:last-of-type {
                            font-size: 24rpx;
                            color: #b4b1b1;
							margin-bottom: 30rpx;
                        }
                    }
                }
                > div {
                    color: #3f72e7;
                }
                > p {
                    color: #ffc403;
                    font-size: 34rpx;
                    text-align: right;
                    margin: 30rpx 0;
                    > span:nth-of-type(1) {
                        font-size: 28rpx;
                        color: black;
                        display: block;
                    }
                }
            }
        }
    }
}
</style>