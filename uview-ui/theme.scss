// 此文件为uView的主题变量，这些变量目前只能通过uni.scss引入才有效，另外由于
// uni.scss中引入的样式会同时混入到全局样式文件和单独每一个页面的样式中，造成微信程序包太大，
// 故uni.scss只建议放scss变量名相关样式，其他的样式可以通过main.js或者App.vue引入

// 使用现代化设计系统的颜色
$u-main-color: #202124;
$u-content-color: #5F6368;
$u-tips-color: #9AA0A6;
$u-light-color: #BDC1C6;
$u-border-color: #E8EAED;
$u-bg-color: #F8F9FA;

$u-type-primary: #1A73E8;
$u-type-primary-light: rgba(26, 115, 232, 0.1);
$u-type-primary-disabled: rgba(26, 115, 232, 0.4);
$u-type-primary-dark: #1557B0;

$u-type-warning: #FBBC04;
$u-type-warning-disabled: rgba(251, 188, 4, 0.4);
$u-type-warning-dark: #F9AB00;
$u-type-warning-light: rgba(251, 188, 4, 0.1);

$u-type-success: #34A853;
$u-type-success-disabled: rgba(52, 168, 83, 0.4);
$u-type-success-dark: #2E7D32;
$u-type-success-light: rgba(52, 168, 83, 0.1);

$u-type-error: #fa3534;
$u-type-error-disabled: #fab6b6;
$u-type-error-dark: #dd6161;
$u-type-error-light: #fef0f0;

$u-type-info: #909399;
$u-type-info-disabled: #c8c9cc;
$u-type-info-dark: #82848a;
$u-type-info-light: #f4f4f5;

$u-form-item-height: 70rpx;
$u-form-item-border-color: #dcdfe6;
