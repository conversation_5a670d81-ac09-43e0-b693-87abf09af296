<template>
    <div id="businessInformation">
        <section class="filter-header">
            <u-dropdown ref="uDropdown" :close-on-click-mask="false" :close-on-click-self="false" @open="openFilterHeader">
                <u-dropdown-item title="筛选">
                    <view class="filter-form">
                        <u-field v-model="params.merchantId" label="商户编号" placeholder="请输入商户编号" />
                        <u-field v-model="params.merchantName" label="商户名称" placeholder="请输入商户名称" />
                        <u-field label='支付通道' placeholder="选择支付通道" :value="payOrgCode" @click="showPayOrgCodes" :right-icon="`arrow-${payOrgCodesPicker ? 'up' : 'down'}`" disabled />
                        <view class="btnTools">
                            <u-button size="medium" @click="toReset">重置</u-button>
                            <u-button type="primary" size="medium" @click="$refs.uDropdown.close(),getBusiness(true)">确定</u-button>
                        </view>
                    </view>
                </u-dropdown-item>
            </u-dropdown>
        </section>
        <option-tab :opts="`我的商户(${number.dirBusCount}),合作伙伴商户(${number.noDirBusCount})`" :current="active" @select="select" />
        <u-select v-model="payOrgCodesPicker" :list="payOrgCodes" label-name="name" value-name="code" @confirm="payOrgCodeConfirm" :default-value="payOrgCode ? [payOrgCodes.findIndex(i=>i.name === payOrgCode)] : []" />
        <main>
            <no-content v-if="show" />

            <section v-show="!show">
                <section v-for="(b, index) in business" :key="index" @click="businessDetails(b.merchantId)">
                    <image :src="head" alt />
                    <div class="right">
                        <div>
                            <p>
                                <span>{{ b.merchantName }} ({{b.payOrgCode | orgCodeFormat}})</span>
                            </p>
                            <p>
                                <span style="color:#666">{{ b.merchantId }}</span>
                            </p>
                        </div>
                        <p>
                            {{ b.createTime ? b.createTime.substring(0, 10) : "" }}
                        </p>
                    </div>
                </section>
            </section>

            <u-loadmore v-if="!show" :status="status" @loadmore="getBusiness(false)" />
        </main>

        <u-back-top :scrollTop="scrollTop" />
    </div>
</template>

<script>
import OptionTab from "../../components/OptionTab.vue"
import { queryMerchantList, getChannel } from "../../http/api"

export default {
    name: "BusinessInformation",
    components: {
        OptionTab,
    },
    data() {
        return {
            payOrgCodesPicker: false,
            payOrgCodes: [],
            payOrgCode: "",
            oldParams: null,
            params: {
                type: 0,
                merchantId: "",
                payOrgCode: '',
                merchantName: '',
                pageNo: 1,
                pageSize: 20
            },
            isDisabled: false,
            active: 0,
            direct: require("../static/images/direct.png"),
            nDirect: require("../static/images/n-direct.png"),
            number: {
                dirBusCount: 0,
                noDirBusCount: 0
            },
            show: false,
            business: [],
            total: null,
            status: 'loading',
            scrollTop: null,
            head: null,
            scrollTop: 0
        };
    },
    onLoad() {
        this.getBusiness(true);
    },
    onPageScroll(e) {
        this.scrollTop = e.scrollTop;
    },
    onReachBottom() {
        if (this.status == 'nomore') return;
        this.getBusiness(false)
    },
    methods: {
        showPayOrgCodes() {
            this.payOrgCodes = [];
            getChannel().then((res) => {
                if (res.code == "00") {
                    if (res.data.length) {
                        this.payOrgCodes = [{ name: "全部", code: '' }, ...res.data]
                        this.payOrgCodesPicker = true;
                    } else {
                        this.$u.toast("暂无可选支付通道！");
                    }
                }
            });
        },
        payOrgCodeConfirm([{ label, value }]) {
            this.payOrgCode = label === '全部' ? '' : label
            this.params.payOrgCode = value
            this.payOrgCodesPicker = false
        },
        openFilterHeader() {
            this.oldParams = JSON.parse(JSON.stringify(this.params))
        },
        select(data) {
            if (this.active == data) return;
            this.active = data;
            this.params.type = data;
            this.getBusiness(true);
        },
        getBusiness(isInquire) {
            if (JSON.stringify(this.oldParams) == JSON.stringify(this.params)) {
                return;
            }
            this.params.pageNo = isInquire ? 1 : this.params.pageNo + 1;
            this.status = 'loading'
            queryMerchantList(this.params).then((res) => {
                if (res.code == "00") {
                    this.head = this.params.type == 0 ? this.direct : this.nDirect;
                    this.total = res.data.pageInfoBean.total;
                    this.number.dirBusCount = res.data.dirBusCount;
                    this.number.noDirBusCount = res.data.noDirBusCount;
                    if (res.data.channelMerchantVoList.length) {
                        this.show = false;
                        !isInquire ? res.data.channelMerchantVoList.forEach((i) => {
                            this.business.push(i);
                        }) : (this.business = res.data.channelMerchantVoList);
                        isInquire && uni.pageScrollTo({ scrollTop: 0 });
                    } else {
                        this.show = true;
                        if (isInquire) {
                            this.business = []
                        }
                    }
                    if (this.business.length >= this.total) {
                        // 数据全部加载完成
                        this.status = 'nomore';
                    } else {
                        this.status = 'loadmore';
                    }
                }
            });
        },
        // 重置参数
        toReset() {
            this.params = {
                type: this.params.type,
                merchantId: "",
                payOrgCode: '',
                merchantName: '',
                pageNo: 1,
                pageSize: 20
            };
            this.payOrgCode = ''
        },
        businessDetails(merchantId) {
            this.$Router.push({ name: 'MerchantDetail', params: { id: merchantId } });
        }
    }
};
</script>

<style lang="less" scoped>
@import "../../static/css/game.less";

#businessInformation {
    main {
        position: relative;
        > section {
            width: 100%;
            padding-bottom: 0.2rpx;
            > section {
                display: flex;
                align-items: center;
                background-color: #fff;

                > image {
                    width: 68rpx;
                    height: 68rpx;
                    margin-left: 30rpx;
                }
                > .right {
                    flex: 1;
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    border-bottom: 2rpx solid #f3f5f7;
                    padding: 30rpx 0;
                    margin-left: 20rpx;
                    div {
                        p {
                            &:last-child {
                                font-size: 24rpx;
                                color: #666666;
                            }
                        }
                    }
                    > p {
                        flex-shrink: 0;
                        padding: 6rpx 12rpx;
                        margin-right: 30rpx;
                        background: #f3f5f7;
                        border-radius: 8rpx;
                        font-size: 24rpx;
                        color: #999999;
                    }
                }
                p {
                    margin: 0;
                    padding: 0;
                }
            }
        }
    }
}
</style>