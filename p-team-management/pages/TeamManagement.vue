<template>
    <div id="team-management">
        <main>
            <no-content v-if="show" />

            <view class="list-data" v-show="!show">
                <div class="list" v-for="(a, index) in agents" :key="index" @click="toAgentTeamDetail(a.agentName, a.agentCode)">
                    <section>
                        <div>
                            <image src="../../static/images/home/<USER>" alt="" />
                            <u-tag v-if="a.isDirect" text="直属" size="mini" shape="circle"/>
                        </div>
                        <div>
                            <p>{{a.agentName || '--'}}</p>
                            <p class="time">注册时间：{{a.createTime}}</p>
                        </div>
                    </section>
                    <p>{{a.mobile ? a.mobile.replace(/(\d{3})\d*(\d{4})/,"$1 **** $2") : ''}}</p>
                </div>
            </view>

            <u-loadmore v-if="!show" :status="status" @loadmore="loadmore" />
        </main>
    </div>
</template>

<script>
import { getDirectAgentList } from '../../http/api-direct';

export default {
    name: 'TeamManagement',
    data() {
        return {
            show: false,
            agents: [],
            total: null,
            status: 'loading',
            scrollTop: null,
        }
    },
    onLoad() {
        this.getAgents();
    },
    onReachBottom() {
        this.loadmore()
    },
    methods: {
        getAgents() {
            getDirectAgentList({ pageNum: 1, pageSize: 20 }).then((res) => {
                if (res.code == '00') {
                    this.total = res.data.total;
                    if (res.data.list.length != 0) {
                        this.show = false;
                        this.agents = res.data.list;
                        if (this.agents.length >= this.total) {
                            // 数据全部加载完成
                            this.status = 'nomore';
                        } else {
                            this.status = 'loadmore';
                        }
                    } else {
                        this.show = true;
                    }
                }
            });
        },
        loadmore() {
            if (this.status == 'nomore') return;
            this.status = 'loading'
            getDirectAgentList({
                pageNum: this.agents.length / 20 + 1,
                pageSize: 20,
            }).then((res) => {
                if (res.code == "00") {
                    this.total = res.data.total;
                    res.data.list.forEach((i) => {
                        this.agents.push(i);
                    });
                    if (this.agents.length >= this.total) {
                        // 数据全部加载完成
                        this.status = 'nomore';
                    } else {
                        this.status = 'loadmore';
                    }
                }
            });
        },
        toAgentTeamDetail(agentName, agentCode) {
            this.$Router.push({ name: "AgentTeamDetail", params: { agentName: agentName || '--', agentCode } })
        }
    }
}
</script>

<style lang="less" scoped>
#team-management {
    height: 100%;
    padding-top: 20rpx;
    > main {
        .list-data {
            background-color: #fff;
            .list {
                border-bottom: 1px solid #e5e5e5;
                padding: 34rpx 30rpx 32rpx 0;
                margin-left: 30rpx;
                display: flex;
                justify-content: space-between;
                align-items: center;
                > section {
                    display: flex;
                    align-items: center;
                    > div {
                        &:first-of-type {
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            margin-right: 20rpx;
                            > image {
                                width: 68rpx;
                                height: 68rpx;
                                margin-bottom: 6rpx;
                            }
                        }
                    }

                    .time {
                        font-size: 24rpx;
                        color: #999999;
                    }
                }
                p {
                    font-size: 28rpx;
                    color: #222222;
                    margin: 0 0 12rpx 0;
                }
            }
        }
    }
}
</style>