<template>
    <div id="withdrawRecord">
        <main>
            <u-sticky>
                <header class="filter-header">
                    <u-dropdown ref="uDropdown" :close-on-click-mask="false" :close-on-click-self="false" @open="openFilterHeader">
                        <u-dropdown-item title="筛选">
                            <view class="filter-form">
                                <section class="filtrate-time">
                                    <u-field disabled :border-bottom="false" :value="startTime" placeholder="开始时间" @click="showStartPicker = true" />
                                    <span>—</span>
                                    <u-field disabled :border-bottom="false" :value="endTime" placeholder="结束时间" @click="showEndPicker = true" />
                                </section>
                                <div class="btnTools">
                                    <u-button size="medium" @click="toReset">重置</u-button>
                                    <u-button size="medium" color="#004ea9" type="primary" @click="$refs.uDropdown.close(), getPageData(true)">确定</u-button>
                                </div>
                            </view>
                        </u-dropdown-item>
                    </u-dropdown>
                </header>
            </u-sticky>

            <no-content v-if="show" />

            <view class="list-data" v-show="!show">
                <div class="card" v-for="(m, index) in pageData" :key="index">
                    <p style="font-weight:bold">{{m.bankNameRemark || ''}}</p>
                    <p>{{m.accountName || ''}}</p>
                    <p>
                        <u-tag size="mini" mode="dark" :type="statusTag[m.status - 1]" :text="withdrawStatus[m.status - 1]" />
                    </p>
                    <p v-if="m.status == 3" class="failMsg">失败描述:{{m.failMsg || '无描述'}}</p>
                    <p>提现金额:<span>{{ m.withdrawAmt | toDecimal2 }}元 </span></p>
                    <p>到账金额:<span>{{ m.arriveAmt | toDecimal2 }} 元</span></p>
                    <p style="color:#aaaaaa">{{m.transDate | dateFormat}}</p>
                </div>
            </view>
            <u-loadmore v-if="!show" :status="status" @loadmore="getPageData(false)" />
        </main>

        <!-- 选择时间 proup -->
        <u-picker v-model="showStartPicker" title="开始时间" :show-time-tag="false" mode="time" @confirm="onConfirmStart" />
        <u-picker v-model="showEndPicker" title="结束时间" :show-time-tag="false" mode="time" @confirm="onConfirmEnd" />
    </div>
</template>

<script>
import { dateFormat, toDecimal2 } from "../../static/utils/date";
import { getWithdrawRecord } from "../../http/api";

export default {
    name: "WithdrawRecord",
    filters: {
        dateFormat,
        toDecimal2
    },
    data() {
        return {
            total: null,
            show: false,
            showStartPicker: false,
            showEndPicker: false,
            startTime: dateFormat(new Date()).substring(0, 10),
            endTime: dateFormat(new Date()).substring(0, 10),
            oldParams: null,
            params: {
                startDate: "",
                endDate: "",
                pageNo: 1,
                pageSize: 10,
            },
            pageData: [],
            status: 'loading',
            withdrawStatus: ['处理中', '提现成功', '提现失败','处理中'],
            statusTag: ['primary', 'success', 'error','primary']
        };
    },
    onLoad() {
        this.getPageData(true);
    },
    onReachBottom() {
        this.getPageData(false);
    },
    methods: {
        openFilterHeader() {
            this.oldParams = JSON.parse(JSON.stringify(this.params))
        },
        toReset() {
            var oldData = this.$options.data();
            this.params = oldData.params;
            this.startTime = oldData.startTime;
            this.endTime = oldData.endTime;
        },
        onConfirmStart(val) {
            this.startTime = dateFormat(val.timestamp * 1000).substring(0, 10);
            this.showStartPicker = false;
        },
        onConfirmEnd(val) {
            this.endTime = dateFormat(val.timestamp * 1000).substring(0, 10);
            this.showEndPicker = false;
        },
        getPageData(isInquire) {
            this.params.startDate =
                this.startTime == "" ? null : this.startTime + " 00:00:00";
            this.params.endDate =
                this.endTime == "" ? null : this.endTime + " 23:59:59";

            if (!isInquire && this.status == 'nomore') return;
            if (isInquire && JSON.stringify(this.oldParams) == JSON.stringify(this.params)) {
                return;
            }
            this.status = 'loading'

            this.params.pageNo = isInquire ? 1 : this.params.pageNo + 1;
            getWithdrawRecord(this.params).then((res) => {
                if (res.code == "00") {
                    if (res.data.list.length != 0) {
                        this.show = false;
                        this.total = res.data.total;
                        !isInquire
                            ? res.data.list.forEach((i) => {
                                this.pageData.push(i);
                            })
                            : (this.pageData = res.data.list);
                        isInquire && uni.pageScrollTo({
                            scrollTop: 0,
                        });
                    } else {
                        this.show = true;
                    }

                    if (this.pageData.length >= this.total) {
                        // 数据全部加载完成
                        this.status = 'nomore';
                    } else {
                        this.status = 'loadmore';
                    }
                }
            });
        },
    }
};
</script>

<style lang="less">
@import "../../static/css/game.less";

#withdrawRecord {
    min-height: 100%;
    background-color: #fff;
    main {
        .list-data {
            padding: 20rpx 30rpx 0;
            .card {
                background: #f3f5f7;
                border-radius: 20rpx;
                padding: 20rpx;
                margin-bottom: 20rpx;
                > p {
                    &:not(:last-of-type) {
                        margin-bottom: 10rpx;
                    }
                }
                .failMsg {
                    color: #e54d42;
                    word-wrap: break-word;
                }
            }
        }
    }
}
</style>