<template>
    <div class="noContent">
        <u-empty :text="tips" :mode="mode" :margin-top="mTop" />
    </div>
</template>

<script>
export default {
    name: "NoContent",
    props: {
        tips: {
            type: String,
            default: '暂无内容'
        },
        mode: {
            type: String,
            default: 'data'
        },
        mTop: {
            type: Number,
            default: 200
        }
    }
}
</script>

<style lang="less" scoped>
.noContent {
    display: flex;
    justify-content: center;
}
</style>