<template>
    <view class="charts-box">
        <qiun-data-charts inScrollView type="area" :opts="chartOptinons" :pageScrollTop="scrollTop || 0" :chartData="chartData" />
    </view>
</template>

<script>
import { getChartDataList } from "../http/api";
import { accDiv } from '../static/utils/date';

export default {
    name: 'LineC<PERSON>',
    props: {
        dataType: {
            type: Number
        },
        payMarketMode: {
            type: Number
        }
    },
    inject: ['pageScrollTop'],
    data() {
        return {
            chartData: {},
            chartOptinons: {
                extra: {
                    area: { type: 'curve', addLine: true, gradient: true, opacity: 0.4 }
                },
                legend: { show: false },
                yAxis: {
                    showTitle: true, data: [{
                        title: '万元',
                        titleFontSize: 11,
                        titleOffsetY: -3,
                        titleFontColor: '#555555',
                    }],
                    splitNumber: 6
                },
                xAxis: { labelCount: 7 }, dataLabel: false, dataPointShape: false
            }
        }
    },
    computed: {
        scrollTop() {
            return this.pageScrollTop.scrollTop
        }
    },
    watch: {
        dataType() {
            this.getChartDataList(this.dataType, this.payMarketMode);
        },
        payMarketMode: {
            handler(old, newVal) {
                if (this.$store.state.token) {
                    this.getChartDataList(this.dataType, this.payMarketMode);
                }
            },
            immediate: true
        }
    },
    methods: {
        getChartDataList(dataType, payMarketMode) {
            getChartDataList({
                dataType: dataType,
                payMarketMode: payMarketMode,
                censusType: 0
            }).then(res => {
                if (res.code == '00') {
                    var xAxis = [], series = [];
                    res.data.forEach((i, index) => {
                        xAxis.push(i.summaryDate);
                        series.push(Number(accDiv(i.totalTransAmount, 10000).toFixed(3).slice(0, -1)));
                        if (index == res.data.length - 1) {
                            const data = {
                                categories: xAxis,
                                series: [{ name: '交易', data: series }]
                            }
                            this.chartData = data
                            console.log(this.chartData)
                        }
                    })
                }
            });
        }
    }
}
</script>
<style lang="scss">
.charts-box {
    width: 100%;
    height: 400rpx;
}
</style>