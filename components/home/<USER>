<template>
  <div class="modern-menu-grid">
    <!-- 主要功能菜单 -->
    <view class="menu-container">
      <view class="menu-grid">
        <view
          v-for="(m, index) in middleCrossMenu"
          :key="index"
          v-if="m.show"
          class="menu-item"
          @click="toLink(m)"
        >
          <view class="menu-icon">
            <image :src="m.img" mode="aspectFit" />
          </view>
          <text class="menu-title">{{ m.title }}</text>
        </view>
      </view>
    </view>

    <!-- 分享按钮 -->
    <view
      v-if="!isApp || (this.$store.state.isIosCheckPass == 1)"
      class="share-button"
      @click="$Router.push({ name: 'Share' })"
    >
      <view class="share-content">
        <view class="share-icon">
          <u-icon name="share" size="32" color="#1A73E8" />
        </view>
        <view class="share-text">
          <text class="share-title">邀请好友</text>
          <text class="share-subtitle">分享赚收益</text>
        </view>
        <view class="share-arrow">
          <u-icon name="arrow-right" size="24" color="#9AA0A6" />
        </view>
      </view>
    </view>
  </div>
</template>

<script>
import { getChnToken } from '../../http/api';
export default {
  name: 'MiddleCrossMenu',
  props: {
    middleCrossMenu: {
      type: Array,
      value: []
    }
  },
  data() {
    return {
      isApp: typeof plus === 'object',
    }
  },
  methods: {
    async toLink(m) {
      if (m.name === 'WebView') {
        const { data } = await getChnToken({ a: '1' });
        if (data.url) this.$Router.push({ name: 'WebView', params: { url: data.url ,title:'万商云SaaS'} });
        return;
      }
      this.$Router.push({ name: m.name });
    }
  }
};
</script>

<style lang="scss" scoped>
.modern-menu-grid {
  padding: 0 $spacing-lg $spacing-lg;

  .menu-container {
    background: $bg-primary;
    border-radius: $radius-large;
    box-shadow: $shadow-light;
    overflow: hidden;
    margin-bottom: $spacing-lg;

    .menu-grid {
      display: flex;
      flex-wrap: wrap;
      padding: $spacing-lg;

      .menu-item {
        width: 25%;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: $spacing-md;
        border-radius: $radius-medium;
        transition: all 0.3s ease;

        &:active {
          background: $bg-tertiary;
          transform: scale(0.95);
        }

        .menu-icon {
          width: 80rpx;
          height: 80rpx;
          background: $bg-tertiary;
          border-radius: $radius-medium;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: $spacing-sm;

          image {
            width: 48rpx;
            height: 48rpx;
          }
        }

        .menu-title {
          font-size: $font-size-sm;
          color: $text-primary;
          text-align: center;
          font-weight: $font-weight-medium;
        }
      }
    }
  }

  .share-button {
    background: $bg-primary;
    border-radius: $radius-large;
    box-shadow: $shadow-light;
    overflow: hidden;
    transition: all 0.3s ease;

    &:active {
      transform: translateY(2rpx);
      box-shadow: $shadow-medium;
    }

    .share-content {
      display: flex;
      align-items: center;
      padding: $spacing-lg;

      .share-icon {
        width: 64rpx;
        height: 64rpx;
        background: rgba(26, 115, 232, 0.1);
        border-radius: $radius-medium;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: $spacing-md;
      }

      .share-text {
        flex: 1;

        .share-title {
          display: block;
          font-size: $font-size-base;
          font-weight: $font-weight-medium;
          color: $text-primary;
          margin-bottom: $spacing-xs;
        }

        .share-subtitle {
          display: block;
          font-size: $font-size-sm;
          color: $text-secondary;
        }
      }

      .share-arrow {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48rpx;
        height: 48rpx;
        border-radius: 50%;
        transition: all 0.3s ease;

        &:active {
          background: $bg-tertiary;
        }
      }
    }
  }
}
</style>
