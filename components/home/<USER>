<template>
    <div class="title apply">
        <p>
            <image src="../../static/images/home/<USER>" alt="" />
            <span>业务申请</span>
        </p>
        <div>
            <!-- <img v-if="currentMode == 1" src="../../static/images/home/<USER>" alt="" @click="toWhichPage('ChangeSettlementPrice')" />
            <img v-if="currentMode == 1" src="../../static/images/home/<USER>" alt="" @click="toWhichPage('ChangeReachCashBackRules')" /> -->
            <image src="../../static/images/home/<USER>" alt="" @click="toWhichPage('PlatformBusiness')" />
            <image src="../../static/images/home/<USER>" alt="" @click="toWhichPage('ChannelBusiness')" />
        </div>
    </div>
</template>

<script>
export default {
    name: 'Apply',
    computed: {
        currentMode() {
            return this.$store.state.currentMode
        }
    },
    methods: {
        toWhichPage(name) {
            this.$Router.push({ name });
        }
    },
}
</script>

<style lang="scss" scoped>
@import "../../static/css/home.scss";
.apply {
    padding-bottom: 0;
    > div {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        > image {
            width: 340rpx;
            height: 108rpx;
            &:nth-of-type(1),
            &:nth-of-type(2) {
                margin-bottom: 10rpx;
            }
        }
    }
}
</style>