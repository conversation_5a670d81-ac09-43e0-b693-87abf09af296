<template>
  <div class="homeChannel">
    <top ref="top" :modeImg="require('../../static/images/home/<USER>')">
      <div slot="wallet" class="modern-wallet">
        <!-- 主要数据卡片 -->
        <view class="wallet-main-card">
          <view class="card-header">
            <text class="card-title">今日概览</text>
            <view class="refresh-btn" @click="getData">
              <u-icon name="reload" size="32" color="#FFFFFF" />
            </view>
          </view>

          <view class="stats-grid">
            <view class="stat-item">
              <view class="stat-icon profit-icon">
                <u-icon name="rmb-circle" size="28" color="#34A853" />
              </view>
              <view class="stat-content">
                <text class="stat-value">{{ wallet.todayProfitAmount | toDecimal2 }}</text>
                <text class="stat-label">今日分润(元)</text>
              </view>
            </view>

            <view class="stat-divider"></view>

            <view class="stat-item">
              <view class="stat-icon flow-icon">
                <u-icon name="order" size="28" color="#1A73E8" />
              </view>
              <view class="stat-content">
                <text class="stat-value">{{ wallet.todayTransAmount | toDecimal2 }}</text>
                <text class="stat-label">今日流水(元)</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 月度数据卡片 -->
        <view class="wallet-month-card">
          <view class="month-header">
            <text class="month-title">本月交易</text>
          </view>

          <view class="month-stats">
            <view class="month-item">
              <view class="month-content">
                <text class="month-value">{{ wallet.monthOneDirectTxnAmt | toDecimal2 }}</text>
                <text class="month-label">直营交易(元)</text>
              </view>
            </view>

            <view class="month-item">
              <view class="month-content">
                <text class="month-value">{{ wallet.monthOneTeamTxnAmt | toDecimal2 }}</text>
                <text class="month-label">团队交易(元)</text>
              </view>
            </view>
          </view>
        </view>
      </div>
    </top>
    <!-- <p class="interval" /> -->
    <main>
      <middle-cross-menu :middleCrossMenu="middleCrossMenu" />
      <p class="interval"></p>
      <!--  #ifdef  MP-WEIXIN -->
      <apply class="title" />
      <!--  #endif -->
      <!--  #ifdef  APP-PLUS -->
      <apply class="title" v-if="this.$store.state.isIosCheckPass == 1" />
      <!--  #endif -->
      <!-- <p class="interval" />
            <statistic-analysis ref="statisticAnalysis" class="title" :payMarketMode="parseInt(1)" /> -->
      <p class="interval"></p>
      <p class="end-line">我是有底线的～</p>
    </main>
  </div>
</template>

<script>
import { getHomeProfit } from '../../http/api';
import { toDecimal2 } from '../../static/utils/date';
import Top from '../../components/home/<USER>';
import MiddleCrossMenu from '../../components/home/<USER>';
import Apply from '../../components/home/<USER>';
import StatisticAnalysis from '../../components/home/<USER>';
import { mapState } from 'vuex';

export default {
  name: 'HomeChannel',
  components: {
    Top,
    MiddleCrossMenu,
    Apply,
    StatisticAnalysis
  },
  filters: {
    toDecimal2
  },
  computed: {
    ...mapState(['userInfo','isIosCheckPass']),
    middleCrossMenu(){
      const _this = this
   return [
        {
          show: true,
          img: require('../../static/images/home/<USER>'),
          title: '服务商',
          name: 'ChannelManagement'
        },
        {
          show: true,
          img: require('../../static/images/home/<USER>'),
          title: '商户',
          name: 'BusinessInformation'
        },
        {
          show: true,
          img: require('../../static/images/home/<USER>'),
          title: '交易推送',
          name: 'ShareProfitDetail'
        },
        {
          show: true,
          img: require('../../static/images/home/<USER>'),
          title: '终端管理',
          name: 'TerminalManagement'
        },
        {
          show: true,
          img: require('../../static/images/home/<USER>'),
          title: '结算政策',
          name: 'SettmentPolicyList'
        },
        {
          show: true,
          img: require('../../static/images/home/<USER>'),
          title: '商户检测',
          name: 'MerchantSearch'
        },
        {
          show: !(typeof plus === 'object') || _this.isIosCheckPass == 1,
          img: require('../../static/images/home/<USER>'),
          title: '商户注册',
          name: 'MerchantsAccessNetwork'
        },
        {
                    show: !(typeof plus === 'object') || _this.isIosCheckPass == 1,
                    img: require('../../static/images/home/<USER>'),
                    title: '物料商城',
                    name: 'MaterialStore'
                },
      ]
    },
  },
  data() {
    return {
      oldTime: null,
      walletVisible: true,
      wallet: {
        todayProfitAmount: '0.00',
        todayTransAmount: '0.00',
        monthOneDirectTxnAmt: '0.00',
        monthOneTeamTxnAmt: '0.00'
      },
      middleCrossMenu: [
        {
          show: true,
          img: require('../../static/images/home/<USER>'),
          title: '服务商',
          name: 'ChannelManagement'
        },
        {
          show: true,
          img: require('../../static/images/home/<USER>'),
          title: '商户',
          name: 'BusinessInformation'
        },
        {
          show: true,
          img: require('../../static/images/home/<USER>'),
          title: '交易推送',
          name: 'ShareProfitDetail'
        },
        {
          show: true,
          img: require('../../static/images/home/<USER>'),
          title: '终端管理',
          name: 'TerminalManagement'
        },
        {
          show: true,
          img: require('../../static/images/home/<USER>'),
          title: '结算政策',
          name: 'SettmentPolicyList'
        },
        {
          show: true,
          img: require('../../static/images/home/<USER>'),
          title: '商户检测',
          name: 'MerchantSearch'
        },
        {
          show: !(typeof plus === 'object') || this.$store.state.isIosCheckPass == 1,
          img: require('../../static/images/home/<USER>'),
          title: '商户注册',
          name: 'MerchantsAccessNetwork'
        },
        {
                    show: !(typeof plus === 'object') || this.$store.state.isIosCheckPass == 1,
                    img: require('../../static/images/home/<USER>'),
                    title: '物料商城',
                    name: 'MaterialStore'
                },
        // //#ifdef  MP-WEIXIN
        // {
        //   show: this.$store.state.userInfo.wsyDisplaySwitch,
        //   img: require('../../static/images/home/<USER>'),
        //   title: '进件配置',
        //   name: 'WebView'
        // },
        // //#endif
        // //#ifdef  APP-PLUS
        // {
        //   show: this.$store.state.userInfo.wsyDisplaySwitch && this.$store.state.isIosCheckPass,
        //   img: require('../../static/images/home/<USER>'),
        //   title: '进件配置',
        //   name: 'WebView'
        // }
        // //#endif
      ]
    };
  },
  methods: {
    refreshData() {
      if (this.oldTime == null) {
        this.oldTime = Date.now();
        this.getData();
        this.$nextTick(() => {
          this.$refs.top.getNotifications();
        });
      } else {
        if (Date.now() > this.oldTime + 2 * 60 * 1000) {
          this.oldTime = Date.now();
          this.getData();
          this.$nextTick(() => {
            this.$refs.top.getNotifications();
          });
        }
      }
    },
    getData() {
      getHomeProfit().then(res => {
        if (res.code == '00') {
          this.wallet = {
            todayProfitAmount: res.data.todayProfitAmount,
            todayTransAmount: res.data.todayTransAmount,
            monthOneDirectTxnAmt: res.data.monthOneDirectTxnAmt,
            monthOneTeamTxnAmt: res.data.monthOneTeamTxnAmt
          };
        }
      });
    },
    toLink(name) {
      this.$Router.push({ name });
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../../static/css/home.scss';

.homeChannel {
  min-height: 100%;
  background-color: transparent;

  .modern-wallet {
    margin-bottom: $spacing-lg;

    // 主要数据卡片
    .wallet-main-card {
      background: rgba(255, 255, 255, 0.95);
      border-radius: $radius-large;
      box-shadow: $shadow-medium;
      margin-bottom: $spacing-md;
      backdrop-filter: blur(10rpx);
      overflow: hidden;

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: $spacing-lg;
        background: rgba(255, 255, 255, 0.1);

        .card-title {
          font-size: $font-size-lg;
          font-weight: $font-weight-semibold;
          color: #FFFFFF;
        }

        .refresh-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 64rpx;
          height: 64rpx;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          transition: all 0.3s ease;

          &:active {
            transform: scale(0.95);
            background: rgba(255, 255, 255, 0.3);
          }
        }
      }

      .stats-grid {
        display: flex;
        align-items: center;
        padding: $spacing-lg;

        .stat-item {
          flex: 1;
          display: flex;
          align-items: center;

          .stat-icon {
            width: 64rpx;
            height: 64rpx;
            border-radius: $radius-medium;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: $spacing-md;

            &.profit-icon {
              background: rgba(52, 168, 83, 0.1);
            }

            &.flow-icon {
              background: rgba(26, 115, 232, 0.1);
            }
          }

          .stat-content {
            .stat-value {
              display: block;
              font-size: $font-size-xl;
              font-weight: $font-weight-bold;
              color: $text-primary;
              margin-bottom: $spacing-xs;
            }

            .stat-label {
              display: block;
              font-size: $font-size-sm;
              color: $text-secondary;
            }
          }
        }

        .stat-divider {
          width: 2rpx;
          height: 60rpx;
          background: $border-light;
          margin: 0 $spacing-md;
        }
      }
    }

    // 月度数据卡片
    .wallet-month-card {
      background: rgba(255, 255, 255, 0.95);
      border-radius: $radius-large;
      box-shadow: $shadow-light;
      backdrop-filter: blur(10rpx);
      overflow: hidden;

      .month-header {
        padding: $spacing-md $spacing-lg;
        background: rgba(255, 255, 255, 0.1);

        .month-title {
          font-size: $font-size-base;
          font-weight: $font-weight-medium;
          color: #FFFFFF;
        }
      }

      .month-stats {
        display: flex;

        .month-item {
          flex: 1;
          padding: $spacing-lg;

          &:first-child {
            border-right: 1rpx solid $border-light;
          }

          .month-content {
            text-align: center;

            .month-value {
              display: block;
              font-size: $font-size-lg;
              font-weight: $font-weight-semibold;
              color: $text-primary;
              margin-bottom: $spacing-xs;
            }

            .month-label {
              display: block;
              font-size: $font-size-sm;
              color: $text-secondary;
            }
          }
        }
      }
    }
  }
}
</style>
