<template>
  <div class="middle-cross-menu">
    <div>
      <template v-for="(m, index) in middleCrossMenu">
        <p :key="index" v-if="m.show" @click="toLink(m)">
          <image :src="m.img" alt="" />
          <span>{{ m.title }}</span>
        </p>
      </template>
    </div>
    <image v-if="!isApp || (this.$store.state.isIosCheckPass == 1)" src="../../static/images/home/<USER>" mode="widthFix" @click="$Router.push({ name: 'Share' })" />
  </div>
</template>

<script>
import { getChnToken } from '../../http/api';
export default {
  name: 'MiddleCrossMenu',
  props: {
    middleCrossMenu: {
      type: Array,
      value: []
    }
  },
  data() {
    return {
      isApp: typeof plus === 'object',
    }
  },
  methods: {
    async toLink(m) {
      if (m.name === 'WebView') {
        const { data } = await getChnToken({ a: '1' });
        if (data.url) this.$Router.push({ name: 'WebView', params: { url: data.url ,title:'万商云SaaS'} });
        return;
      }
      this.$Router.push({ name: m.name });
    }
  }
};
</script>

<style lang="less" scoped>
.middle-cross-menu {
  padding: 20rpx 30rpx;
  font-size: 0;
  > div {
    background: #f3f5f7;
    border-radius: 20rpx;
    display: flex;
    flex-wrap: wrap;
    > p {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 25%;
      margin: 0;
      padding: 30rpx 0;
      > image {
        width: 52rpx;
        height: 52rpx;
        margin-bottom: 12rpx;
      }
      > span {
        font-size: 24rpx;
        color: #666666;
      }
    }
  }
  > image {
    width: 100%;
    margin-top: 22rpx;
  }
}
</style>
