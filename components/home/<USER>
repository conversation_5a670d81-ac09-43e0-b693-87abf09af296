<template>
  <div class="homeChannel">
    <top ref="top" :modeImg="require('../../static/images/home/<USER>')">
      <div slot="wallet" class="wallet">
        <div>
          <section>
            <p>
              <image src="../../static/images/home/<USER>" alt="" />
              <span>今日分润(元)</span>
            </p>
            <span>{{ wallet.todayProfitAmount | toDecimal2 }}</span>
          </section>
          <section></section>
          <section>
            <p>
              <image src="../../static/images/home/<USER>" alt="" />
              <span>今日流水(元)</span>
            </p>
            <span>{{ wallet.todayTransAmount | toDecimal2 }}</span>
          </section>
          <!-- <view class="eyes-operate">
                        <u-icon :name="walletVisible ? 'eye' : 'eye-off'" size="40" @click="walletVisible = !walletVisible" />
                    </view> -->
          <div class="refresh" @click="getData">
            <!-- <text>刷新</text> -->
            <u-icon name="reload" size="36" color="#004ea9" />
          </div>
        </div>
        <div>
          <section>
            <p>
              <image src="../../static/images/home/<USER>" alt="" />
              <span>本月直营交易(元)</span>
            </p>
            <span>{{ wallet.monthOneDirectTxnAmt | toDecimal2 }}</span>
          </section>
          <section></section>
          <section>
            <p>
              <image src="../../static/images/home/<USER>" alt="" />
              <span>本月团队交易(元)</span>
            </p>
            <span>{{ wallet.monthOneTeamTxnAmt | toDecimal2 }}</span>
          </section>
        </div>
        <!-- <view class="withdraw-btn">
                    <u-button @click="toLink('WithdrawMoney')" type="primary">收益</u-button>
                </view> -->
      </div>
    </top>
    <!-- <p class="interval" /> -->
    <main>
      <middle-cross-menu :middleCrossMenu="middleCrossMenu" />
      <p class="interval"></p>
      <!--  #ifdef  MP-WEIXIN -->
      <apply class="title" />
      <!--  #endif -->
      <!--  #ifdef  APP-PLUS -->
      <apply class="title" v-if="this.$store.state.isIosCheckPass == 1" />
      <!--  #endif -->
      <!-- <p class="interval" />
            <statistic-analysis ref="statisticAnalysis" class="title" :payMarketMode="parseInt(1)" /> -->
      <p class="interval"></p>
      <p class="end-line">我是有底线的～</p>
    </main>
  </div>
</template>

<script>
import { getHomeProfit } from '../../http/api';
import { toDecimal2 } from '../../static/utils/date';
import Top from '../../components/home/<USER>';
import MiddleCrossMenu from '../../components/home/<USER>';
import Apply from '../../components/home/<USER>';
import StatisticAnalysis from '../../components/home/<USER>';
import { mapState } from 'vuex';

export default {
  name: 'HomeChannel',
  components: {
    Top,
    MiddleCrossMenu,
    Apply,
    StatisticAnalysis
  },
  filters: {
    toDecimal2
  },
  computed: {
    ...mapState(['userInfo','isIosCheckPass']),
    middleCrossMenu(){
      const _this = this
   return [
        {
          show: true,
          img: require('../../static/images/home/<USER>'),
          title: '服务商',
          name: 'ChannelManagement'
        },
        {
          show: true,
          img: require('../../static/images/home/<USER>'),
          title: '商户',
          name: 'BusinessInformation'
        },
        {
          show: true,
          img: require('../../static/images/home/<USER>'),
          title: '交易推送',
          name: 'ShareProfitDetail'
        },
        {
          show: true,
          img: require('../../static/images/home/<USER>'),
          title: '终端管理',
          name: 'TerminalManagement'
        },
        {
          show: true,
          img: require('../../static/images/home/<USER>'),
          title: '结算政策',
          name: 'SettmentPolicyList'
        },
        {
          show: true,
          img: require('../../static/images/home/<USER>'),
          title: '商户检测',
          name: 'MerchantSearch'
        },
        {
          show: !(typeof plus === 'object') || _this.isIosCheckPass == 1,
          img: require('../../static/images/home/<USER>'),
          title: '商户注册',
          name: 'MerchantsAccessNetwork'
        },
        {
                    show: !(typeof plus === 'object') || _this.isIosCheckPass == 1,
                    img: require('../../static/images/home/<USER>'),
                    title: '物料商城',
                    name: 'MaterialStore'
                },
      ]
    },
  },
  data() {
    return {
      oldTime: null,
      walletVisible: true,
      wallet: {
        todayProfitAmount: '0.00',
        todayTransAmount: '0.00',
        monthOneDirectTxnAmt: '0.00',
        monthOneTeamTxnAmt: '0.00'
      },
      middleCrossMenu: [
        {
          show: true,
          img: require('../../static/images/home/<USER>'),
          title: '服务商',
          name: 'ChannelManagement'
        },
        {
          show: true,
          img: require('../../static/images/home/<USER>'),
          title: '商户',
          name: 'BusinessInformation'
        },
        {
          show: true,
          img: require('../../static/images/home/<USER>'),
          title: '交易推送',
          name: 'ShareProfitDetail'
        },
        {
          show: true,
          img: require('../../static/images/home/<USER>'),
          title: '终端管理',
          name: 'TerminalManagement'
        },
        {
          show: true,
          img: require('../../static/images/home/<USER>'),
          title: '结算政策',
          name: 'SettmentPolicyList'
        },
        {
          show: true,
          img: require('../../static/images/home/<USER>'),
          title: '商户检测',
          name: 'MerchantSearch'
        },
        {
          show: !(typeof plus === 'object') || this.$store.state.isIosCheckPass == 1,
          img: require('../../static/images/home/<USER>'),
          title: '商户注册',
          name: 'MerchantsAccessNetwork'
        },
        {
                    show: !(typeof plus === 'object') || this.$store.state.isIosCheckPass == 1,
                    img: require('../../static/images/home/<USER>'),
                    title: '物料商城',
                    name: 'MaterialStore'
                },
        // //#ifdef  MP-WEIXIN
        // {
        //   show: this.$store.state.userInfo.wsyDisplaySwitch,
        //   img: require('../../static/images/home/<USER>'),
        //   title: '进件配置',
        //   name: 'WebView'
        // },
        // //#endif
        // //#ifdef  APP-PLUS
        // {
        //   show: this.$store.state.userInfo.wsyDisplaySwitch && this.$store.state.isIosCheckPass,
        //   img: require('../../static/images/home/<USER>'),
        //   title: '进件配置',
        //   name: 'WebView'
        // }
        // //#endif
      ]
    };
  },
  methods: {
    refreshData() {
      if (this.oldTime == null) {
        this.oldTime = Date.now();
        this.getData();
        this.$nextTick(() => {
          this.$refs.top.getNotifications();
        });
      } else {
        if (Date.now() > this.oldTime + 2 * 60 * 1000) {
          this.oldTime = Date.now();
          this.getData();
          this.$nextTick(() => {
            this.$refs.top.getNotifications();
          });
        }
      }
    },
    getData() {
      getHomeProfit().then(res => {
        if (res.code == '00') {
          this.wallet = {
            todayProfitAmount: res.data.todayProfitAmount,
            todayTransAmount: res.data.todayTransAmount,
            monthOneDirectTxnAmt: res.data.monthOneDirectTxnAmt,
            monthOneTeamTxnAmt: res.data.monthOneTeamTxnAmt
          };
        }
      });
    },
    toLink(name) {
      this.$Router.push({ name });
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../../static/css/home.scss';

.homeChannel {
  min-height: 100%;
  background-color: #ffffff;
  .wallet {
    position: relative;
    // height: 300rpx;
    margin: 0 30rpx;
    border-radius: 20rpx;
    background-color: rgba(255, 255, 255, 0.9);
    > div {
      display: flex;
      padding-top: 60rpx;
      &:nth-of-type(2) {
        padding-top: 0;
        padding-bottom: 20rpx;
      }
      > .refresh {
        position: absolute;
        right: 20rpx;
        top: 10rpx;
        display: flex;
        align-items: center;
        > text {
          margin-right: 12rpx;
          color: #c5c1c1;
          font-size: 24rpx;
        }
      }
      > section {
        &:nth-of-type(1),
        &:nth-of-type(3) {
          width: 344rpx;
          // padding: 60rpx 0 44rpx 50rpx;
          padding-left: 50rpx;
          > p {
            margin: 0 0 8rpx;
            > image {
              width: 12rpx;
              height: 12rpx;
              margin: 6rpx 12rpx 6rpx 0;
            }
            > span {
              font-size: 24rpx;
              color: #666666;
            }
          }
          > span {
            font-size: 36rpx;
            color: #ce0010;
            font-weight: 600;
            margin-left: 24rpx;
          }
        }
        &:nth-of-type(2) {
          width: 2rpx;
          height: 24rpx;
          background-color: #e5e5e5;
          opacity: 0.95;
          border-radius: 0rpx 2rpx 0rpx 0rpx;
          margin: 44rpx 0 50rpx;
        }
      }
    }
    > .withdraw-btn {
      width: 570rpx;
      height: 78rpx;
      font-size: 26rpx;
      color: white;
      border-radius: 10rpx;
      margin: 0 auto 6rpx;
      /deep/ .u-btn--primary {
        border-color: $theme-color;
        background-color: $theme-color;
      }
    }
  }
}
</style>
