<template>
  <div class="modern-top">
    <!-- 背景装饰 -->
    <view class="background-decoration">
      <view class="decoration-circle decoration-circle-1"></view>
      <view class="decoration-circle decoration-circle-2"></view>
      <view class="decoration-wave"></view>
    </view>

    <!-- 主要内容区域 -->
    <section class="content-section">
      <!-- 模式切换 -->
      <view v-if="userInfo.payMarketMode == 0" class="mode-switcher">
        <view class="mode-indicator">
          <view class="mode-badge">
            <u-icon name="setting" size="24" color="#FFFFFF" />
            <text class="mode-text">{{ currentMode == 1 ? '渠道' : '直营' }}模式</text>
          </view>
          <view class="mode-switch" @click="changeMode">
            <u-icon name="arrow-right" size="28" color="#FFFFFF" />
          </view>
        </view>
      </view>

      <!-- 钱包信息卡片 -->
      <view class="wallet-container">
        <slot name="wallet"></slot>
      </view>

      <!-- 轮播图 -->
      <view class="banner-container" v-if="banners.length">
        <u-swiper
          :list="banners"
          name="url"
          :border-radius="16"
          :height="320"
          @click="toBannerAdvertisement"
        />
      </view>

      <!-- 公告栏 -->
      <view class="notice-container" v-show="!!latestNewsTitle">
        <view class="notice-card">
          <view class="notice-icon">
            <u-icon name="volume" size="32" color="#1A73E8" />
          </view>
          <view class="notice-content" @click="toNewsDetail">
            <text class="notice-label">官方公告</text>
            <text class="notice-title">{{ latestNewsTitle }}</text>
          </view>
          <view class="notice-action" @click="toNews">
            <u-icon name="arrow-right" size="24" color="#9AA0A6" />
          </view>
        </view>
      </view>
    </section>

    <!-- 公告弹框 -->
    <u-popup
      v-model="showNewsModel"
      class="news-popup"
      mode="center"
      width="70%"
      border-radius="20"
      :mask-close-able="false"
      closeable
      close-icon="close-circle-fill"
      close-icon-color="#fff"
      close-icon-size="70"
      close-icon-pos="bottom-left"
    >
      <view class="news-model">
        <image
          src="../../static/images/home/<USER>"
          mode="widthFix"
        ></image>
        <view class="news-title">{{ latestNews.noticeTitle }}</view>
        <scroll-view scroll-y="true" style="height: 340rpx">
          <view class="news-content">
            <rich-text :nodes="latestNews.noticeContent"></rich-text>
          </view>
        </scroll-view>
      </view>
    </u-popup>
  </div>
</template>

<script>
  import { getAllNotifications, getSystemParamImgUrl } from '../../http/api';
  import { mapState } from 'vuex';
  import dayjs from 'dayjs';

  export default {
    name: 'Top',
    props: {
      modeImg: {
        type: String
      }
    },
    data() {
      return {
        latestNewsTitle: '',
        latestNews: {},
        showNewsModel: false,
        isInit: true,
        banners: []
      };
    },
    computed: {
      ...mapState(['userInfo', 'currentMode', 'appNotifications'])
    },
    mounted() {
      if (
        !(typeof plus === 'object') ||
        this.$store.state.isIosCheckPass == 1
      ) {
        this.getBanners();
      }
    },
    methods: {
      getBanners() {
        getSystemParamImgUrl({ tip: '占位' }).then((res) => {
          if (res.code == '00') {
            const bannerIds = [];
            Object.keys(res.data || {})
              .filter((key) => key.includes('appBannerImgUrl'))
              .forEach((i) => {
                bannerIds.push(i.replace('appBannerImgUrl', ''));
              });

            bannerIds.forEach((id) => {
              if (res.data[`appBannerImgUrl${id}`]) {
                this.banners.push({
                  url:
                    res.data[`appBannerImgUrl${id}`] +
                    `?number=${Math.random()}`,
                  jumpStatus: res.data[`appBanner${id}JumpStatus`],
                  jumpUrl: res.data[`appBanner${id}JumpUrl`]
                });
              }
            });
          }
        });
      },
      changeMode() {
        if (this.currentMode == 1) {
          this.$store.commit('SET_CURRENTMODE', 2);
        } else {
          this.$store.commit('SET_CURRENTMODE', 1);
        }
      },
      toBannerAdvertisement(index) {
        const { jumpStatus, jumpUrl } = this.banners[index];
        if (!jumpStatus) return;
        switch (jumpStatus) {
          case 1:
            // #ifdef APP-PLUS
            plus.runtime.openURL(jumpUrl);
            // #endif
            // #ifdef MP
            uni.showModal({
              content: '请复制链接前往浏览器查看！',
              confirmText: '复制链接',
              success: (val) => {
                if (val.confirm) {
                  uni.setClipboardData({
                    data: jumpUrl
                  });
                }
              }
            });
            // #endif
            break;
          case 2:
            this.$Router.push({ name: jumpUrl });
            break;
          default:
            this.$u.toast('jumpStatus 状态未知!');
            break;
        }
      },
      toNewsDetail() {
        this.$Router.push({
          name: 'Notification',
          params: { id: this.latestNews.id, announceType: this.latestNews.type }
        });
      },
      toNews() {
        this.$Router.push({ name: 'Notifications' });
      },
      getNotifications() {
        getAllNotifications().then((res) => {
          if (res.code == '00') {
            var { officialNOtice = [], systemNotice = [] } = res.data;
            officialNOtice
              ? officialNOtice.forEach((o) => (o.type = 0))
              : (officialNOtice = []);
            systemNotice
              ? systemNotice.forEach((s) => (s.type = 1))
              : (systemNotice = []);
            this.latestNewsTitle = officialNOtice[0]?.noticeTitle;
            this.latestNews = officialNOtice[0] || {};
            if (this.latestNewsTitle) {
              const intervalDays = dayjs().diff(
                this.latestNews.createTime,
                'day'
              );
              if (intervalDays > 10) {
                this.latestNewsTitle = '';
              }
            }
            if (this.isInit) {
              const shownLatestNewsId = uni.getStorageSync('shownLatestNewsId');
              if (
                this.latestNews.id !== shownLatestNewsId &&
                this.latestNews.createTime
              ) {
                const diffDay = dayjs(this.latestNews.createTime).diff(
                  new Date(),
                  'day'
                );
                if (!(diffDay < -3)) {
                  this.showNewsModel = true;
                  this.isInit = false;
                  uni.setStorageSync('shownLatestNewsId', this.latestNews.id);
                }
              }
            }
            var appNotificationsMap = [...officialNOtice, ...systemNotice];
            const appNotifications = [];
            appNotificationsMap.forEach((i) => {
              appNotifications.push({
                id: i.id,
                type: i.type,
                noticeTitle: i.noticeTitle,
                noticeContent: i.noticeContent,
                createTime: i.createTime,
                readingSatus: 0 // 未读
              });
            });
            const storageNotifications =
              uni.getStorageSync('appNotifications') || [];
            if (storageNotifications.length) {
              appNotifications.forEach((i) => {
                storageNotifications.forEach((j) => {
                  if (i.id == j.id && i.type == j.type) {
                    i.readingSatus = j.readingSatus;
                  }
                });
              });
            }
            this.$store.commit('SET_APPNOTIFICATIONS', appNotifications);
          }
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
.modern-top {
  position: relative;
  background: $gradient-primary;
  overflow: hidden;

  // 背景装饰
  .background-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;

    .decoration-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);

      &.decoration-circle-1 {
        width: 200rpx;
        height: 200rpx;
        top: -100rpx;
        right: -50rpx;
        animation: float 6s ease-in-out infinite;
      }

      &.decoration-circle-2 {
        width: 120rpx;
        height: 120rpx;
        bottom: 100rpx;
        left: -60rpx;
        animation: float 8s ease-in-out infinite reverse;
      }
    }

    .decoration-wave {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 60rpx;
      background: $bg-secondary;
      border-radius: 60rpx 60rpx 0 0;
    }
  }

  // 主要内容区域
  .content-section {
    position: relative;
    z-index: 1;
    padding: $spacing-lg;

    // 模式切换器
    .mode-switcher {
      margin-bottom: $spacing-lg;

      .mode-indicator {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .mode-badge {
          display: flex;
          align-items: center;
          background: rgba(255, 255, 255, 0.2);
          padding: $spacing-sm $spacing-md;
          border-radius: $radius-large;
          backdrop-filter: blur(10rpx);

          .mode-text {
            margin-left: $spacing-sm;
            font-size: $font-size-base;
            font-weight: $font-weight-medium;
            color: #FFFFFF;
          }
        }

        .mode-switch {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 64rpx;
          height: 64rpx;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          backdrop-filter: blur(10rpx);
          transition: all 0.3s ease;

          &:active {
            transform: scale(0.95);
            background: rgba(255, 255, 255, 0.3);
          }
        }
      }
    }

    // 钱包容器
    .wallet-container {
      margin-bottom: $spacing-lg;
    }

    // 轮播图容器
    .banner-container {
      margin-bottom: $spacing-lg;
      border-radius: $radius-medium;
      overflow: hidden;
      box-shadow: $shadow-medium;
    }

    // 公告容器
    .notice-container {
      .notice-card {
        display: flex;
        align-items: center;
        background: $bg-primary;
        border-radius: $radius-medium;
        padding: $spacing-md;
        box-shadow: $shadow-light;

        .notice-icon {
          width: 64rpx;
          height: 64rpx;
          background: rgba(26, 115, 232, 0.1);
          border-radius: $radius-medium;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: $spacing-md;
        }

        .notice-content {
          flex: 1;

          .notice-label {
            display: block;
            font-size: $font-size-sm;
            color: $text-secondary;
            margin-bottom: $spacing-xs;
          }

          .notice-title {
            display: block;
            font-size: $font-size-base;
            font-weight: $font-weight-medium;
            color: $text-primary;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .notice-action {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 48rpx;
          height: 48rpx;
          border-radius: 50%;
          transition: all 0.3s ease;

          &:active {
            background: $bg-tertiary;
          }
        }
      }
    }
  }
}

// 动画
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20rpx);
  }
}

// 新闻弹窗样式
/deep/ .news-popup .u-mode-center-box {
  .news-model {
    background: $bg-primary;
    border-radius: $radius-large;
    overflow: hidden;

    > image {
      width: 100%;
      height: 200rpx;
      object-fit: cover;
    }

    .news-title {
      padding: $spacing-lg;
      text-align: center;
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      color: $text-primary;
      border-bottom: 1rpx solid $border-light;
    }

    .news-content {
      padding: $spacing-lg;
      font-size: $font-size-base;
      line-height: $line-height-relaxed;
      color: $text-primary;

      /deep/ * {
        max-width: 100% !important;
        height: auto !important;
      }
    }
  }
}
</style>
