<template>
  <button 
    class="modern-button"
    :class="[
      `btn-${type}`,
      `btn-${size}`,
      { 
        'btn-loading': loading,
        'btn-disabled': disabled,
        'btn-block': block,
        'btn-round': round
      }
    ]"
    :disabled="disabled || loading"
    @click="handleClick"
  >
    <view v-if="loading" class="btn-loading-icon">
      <u-loading-icon mode="circle" :size="loadingSize" :color="loadingColor" />
    </view>
    
    <view v-if="icon && !loading" class="btn-icon">
      <u-icon :name="icon" :size="iconSize" :color="iconColor" />
    </view>
    
    <text v-if="text || $slots.default" class="btn-text">
      <slot>{{ text }}</slot>
    </text>
  </button>
</template>

<script>
export default {
  name: 'ModernButton',
  props: {
    text: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'primary', // primary, secondary, success, warning, error, text, ghost
      validator: value => ['primary', 'secondary', 'success', 'warning', 'error', 'text', 'ghost'].includes(value)
    },
    size: {
      type: String,
      default: 'medium', // small, medium, large
      validator: value => ['small', 'medium', 'large'].includes(value)
    },
    icon: {
      type: String,
      default: ''
    },
    loading: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    block: {
      type: Boolean,
      default: false
    },
    round: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    iconSize() {
      const sizeMap = {
        small: 24,
        medium: 28,
        large: 32
      }
      return sizeMap[this.size]
    },
    loadingSize() {
      const sizeMap = {
        small: 20,
        medium: 24,
        large: 28
      }
      return sizeMap[this.size]
    },
    iconColor() {
      if (this.type === 'primary' || this.type === 'success' || this.type === 'warning' || this.type === 'error') {
        return '#FFFFFF'
      }
      if (this.type === 'secondary' || this.type === 'ghost') {
        return this.getTypeColor()
      }
      if (this.type === 'text') {
        return this.getTypeColor()
      }
      return '#FFFFFF'
    },
    loadingColor() {
      return this.iconColor
    }
  },
  methods: {
    handleClick() {
      if (!this.disabled && !this.loading) {
        this.$emit('click')
      }
    },
    getTypeColor() {
      const colorMap = {
        primary: '#1A73E8',
        secondary: '#1A73E8',
        success: '#34A853',
        warning: '#FBBC04',
        error: '#EA4335',
        text: '#1A73E8',
        ghost: '#1A73E8'
      }
      return colorMap[this.type] || '#1A73E8'
    }
  }
}
</script>

<style lang="scss" scoped>
.modern-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: $radius-medium;
  font-weight: $font-weight-medium;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  
  &:active {
    transform: translateY(2rpx);
  }
  
  &.btn-disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
  }
  
  &.btn-loading {
    cursor: not-allowed;
  }
  
  &.btn-block {
    width: 100%;
  }
  
  &.btn-round {
    border-radius: 999rpx;
  }
  
  // 尺寸
  &.btn-small {
    height: 64rpx;
    padding: 0 $spacing-md;
    font-size: $font-size-sm;
    
    .btn-icon {
      margin-right: $spacing-xs;
    }
  }
  
  &.btn-medium {
    height: 88rpx;
    padding: 0 $spacing-lg;
    font-size: $font-size-base;
    
    .btn-icon {
      margin-right: $spacing-sm;
    }
  }
  
  &.btn-large {
    height: 112rpx;
    padding: 0 $spacing-xl;
    font-size: $font-size-lg;
    
    .btn-icon {
      margin-right: $spacing-md;
    }
  }
  
  // 类型样式
  &.btn-primary {
    background: $gradient-primary;
    color: $bg-primary;
    box-shadow: $shadow-medium;
    
    &:active {
      box-shadow: $shadow-light;
    }
  }
  
  &.btn-secondary {
    background: $bg-primary;
    color: $primary-color;
    border: 2rpx solid $primary-color;
    
    &:active {
      background: $primary-color;
      color: $bg-primary;
    }
  }
  
  &.btn-success {
    background: $gradient-secondary;
    color: $bg-primary;
    box-shadow: $shadow-medium;
    
    &:active {
      box-shadow: $shadow-light;
    }
  }
  
  &.btn-warning {
    background: linear-gradient(135deg, #FBBC04 0%, #F9AB00 100%);
    color: $text-primary;
    box-shadow: $shadow-medium;
    
    &:active {
      box-shadow: $shadow-light;
    }
  }
  
  &.btn-error {
    background: linear-gradient(135deg, #EA4335 0%, #D33B2C 100%);
    color: $bg-primary;
    box-shadow: $shadow-medium;
    
    &:active {
      box-shadow: $shadow-light;
    }
  }
  
  &.btn-text {
    background: transparent;
    color: $primary-color;
    
    &:active {
      background: rgba(26, 115, 232, 0.08);
    }
  }
  
  &.btn-ghost {
    background: transparent;
    color: $primary-color;
    border: 2rpx solid $primary-color;
    
    &:active {
      background: rgba(26, 115, 232, 0.08);
    }
  }
  
  .btn-loading-icon {
    margin-right: $spacing-sm;
  }
  
  .btn-text {
    white-space: nowrap;
  }
}
</style>
