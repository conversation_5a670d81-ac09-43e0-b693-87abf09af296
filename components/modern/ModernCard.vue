<template>
  <view 
    class="modern-card" 
    :class="[
      `card-${variant}`,
      { 'card-hoverable': hoverable, 'card-clickable': clickable }
    ]"
    :style="cardStyle"
    @click="handleClick"
  >
    <!-- 卡片头部 -->
    <view v-if="$slots.header || title || subtitle" class="card-header">
      <view v-if="title || subtitle" class="header-content">
        <text v-if="title" class="card-title">{{ title }}</text>
        <text v-if="subtitle" class="card-subtitle">{{ subtitle }}</text>
      </view>
      <slot name="header"></slot>
    </view>
    
    <!-- 卡片主体 -->
    <view class="card-body" :style="bodyStyle">
      <slot></slot>
    </view>
    
    <!-- 卡片底部 -->
    <view v-if="$slots.footer" class="card-footer">
      <slot name="footer"></slot>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ModernCard',
  props: {
    title: {
      type: String,
      default: ''
    },
    subtitle: {
      type: String,
      default: ''
    },
    variant: {
      type: String,
      default: 'default', // default, primary, success, warning, error
      validator: value => ['default', 'primary', 'success', 'warning', 'error'].includes(value)
    },
    hoverable: {
      type: Boolean,
      default: false
    },
    clickable: {
      type: Boolean,
      default: false
    },
    shadow: {
      type: String,
      default: 'light', // none, light, medium, heavy
      validator: value => ['none', 'light', 'medium', 'heavy'].includes(value)
    },
    radius: {
      type: String,
      default: 'medium', // small, medium, large, xl
      validator: value => ['small', 'medium', 'large', 'xl'].includes(value)
    },
    padding: {
      type: String,
      default: 'medium' // small, medium, large
    },
    margin: {
      type: String,
      default: ''
    },
    background: {
      type: String,
      default: ''
    }
  },
  computed: {
    cardStyle() {
      const style = {}
      
      if (this.background) {
        style.background = this.background
      }
      
      if (this.margin) {
        style.margin = this.margin
      }
      
      return style
    },
    bodyStyle() {
      const paddingMap = {
        small: '$spacing-sm',
        medium: '$spacing-lg',
        large: '$spacing-xl'
      }
      
      return {
        padding: paddingMap[this.padding] || '$spacing-lg'
      }
    }
  },
  methods: {
    handleClick() {
      if (this.clickable) {
        this.$emit('click')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.modern-card {
  background: $bg-primary;
  border-radius: $radius-medium;
  box-shadow: $shadow-light;
  overflow: hidden;
  transition: all 0.3s ease;
  
  &.card-hoverable:hover {
    transform: translateY(-4rpx);
    box-shadow: $shadow-medium;
  }
  
  &.card-clickable:active {
    transform: translateY(2rpx);
    box-shadow: $shadow-light;
  }
  
  // 变体样式
  &.card-primary {
    border-left: 8rpx solid $primary-color;
  }
  
  &.card-success {
    border-left: 8rpx solid $secondary-color;
  }
  
  &.card-warning {
    border-left: 8rpx solid $warning-color;
  }
  
  &.card-error {
    border-left: 8rpx solid $error-color;
  }
  
  // 阴影变体
  &.shadow-none {
    box-shadow: none;
  }
  
  &.shadow-medium {
    box-shadow: $shadow-medium;
  }
  
  &.shadow-heavy {
    box-shadow: $shadow-heavy;
  }
  
  // 圆角变体
  &.radius-small {
    border-radius: $radius-small;
  }
  
  &.radius-large {
    border-radius: $radius-large;
  }
  
  &.radius-xl {
    border-radius: $radius-xl;
  }
  
  .card-header {
    padding: $spacing-lg;
    border-bottom: 1rpx solid $border-light;
    
    .header-content {
      .card-title {
        display: block;
        font-size: $font-size-lg;
        font-weight: $font-weight-semibold;
        color: $text-primary;
        margin-bottom: $spacing-xs;
      }
      
      .card-subtitle {
        display: block;
        font-size: $font-size-sm;
        color: $text-secondary;
      }
    }
  }
  
  .card-body {
    padding: $spacing-lg;
  }
  
  .card-footer {
    padding: $spacing-lg;
    border-top: 1rpx solid $border-light;
    background: $bg-tertiary;
  }
}
</style>
