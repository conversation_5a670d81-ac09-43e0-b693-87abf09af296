<template>
  <view class="modern-stat-card" :class="[`card-${variant}`]" @click="handleClick">
    <view class="stat-header">
      <view class="stat-icon" v-if="icon" :style="{ background: iconBackground }">
        <u-icon :name="icon" :size="32" :color="iconColor" />
      </view>
      <view class="stat-trend" v-if="trend">
        <u-icon 
          :name="trend > 0 ? 'arrow-up' : 'arrow-down'" 
          :size="20" 
          :color="trend > 0 ? '#34A853' : '#EA4335'" 
        />
        <text :style="{ color: trend > 0 ? '#34A853' : '#EA4335' }">
          {{ Math.abs(trend) }}%
        </text>
      </view>
    </view>
    
    <view class="stat-content">
      <text class="stat-value" :style="{ color: valueColor }">{{ formattedValue }}</text>
      <text class="stat-label">{{ label }}</text>
      <text v-if="subtitle" class="stat-subtitle">{{ subtitle }}</text>
    </view>
    
    <view v-if="showProgress && progress !== undefined" class="stat-progress">
      <view class="progress-bar">
        <view 
          class="progress-fill" 
          :style="{ 
            width: progress + '%', 
            background: progressColor 
          }"
        ></view>
      </view>
      <text class="progress-text">{{ progress }}%</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ModernStatCard',
  props: {
    value: {
      type: [Number, String],
      required: true
    },
    label: {
      type: String,
      required: true
    },
    subtitle: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: ''
    },
    variant: {
      type: String,
      default: 'default', // default, primary, success, warning, error
      validator: value => ['default', 'primary', 'success', 'warning', 'error'].includes(value)
    },
    trend: {
      type: Number,
      default: null // 正数表示上升，负数表示下降
    },
    progress: {
      type: Number,
      default: undefined // 0-100
    },
    showProgress: {
      type: Boolean,
      default: false
    },
    clickable: {
      type: Boolean,
      default: false
    },
    prefix: {
      type: String,
      default: ''
    },
    suffix: {
      type: String,
      default: ''
    },
    decimal: {
      type: Number,
      default: 0
    }
  },
  computed: {
    formattedValue() {
      let val = this.value
      if (typeof val === 'number' && this.decimal > 0) {
        val = val.toFixed(this.decimal)
      }
      return `${this.prefix}${val}${this.suffix}`
    },
    iconBackground() {
      const colorMap = {
        default: 'rgba(26, 115, 232, 0.1)',
        primary: 'rgba(26, 115, 232, 0.1)',
        success: 'rgba(52, 168, 83, 0.1)',
        warning: 'rgba(251, 188, 4, 0.1)',
        error: 'rgba(234, 67, 53, 0.1)'
      }
      return colorMap[this.variant]
    },
    iconColor() {
      const colorMap = {
        default: '#1A73E8',
        primary: '#1A73E8',
        success: '#34A853',
        warning: '#FBBC04',
        error: '#EA4335'
      }
      return colorMap[this.variant]
    },
    valueColor() {
      const colorMap = {
        default: '#202124',
        primary: '#1A73E8',
        success: '#34A853',
        warning: '#FBBC04',
        error: '#EA4335'
      }
      return colorMap[this.variant]
    },
    progressColor() {
      return this.iconColor
    }
  },
  methods: {
    handleClick() {
      if (this.clickable) {
        this.$emit('click')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.modern-stat-card {
  background: $bg-primary;
  border-radius: $radius-medium;
  box-shadow: $shadow-light;
  padding: $spacing-lg;
  transition: all 0.3s ease;
  
  &.card-primary {
    background: linear-gradient(135deg, rgba(26, 115, 232, 0.05) 0%, rgba(66, 133, 244, 0.05) 100%);
    border-left: 4rpx solid #1A73E8;
  }
  
  &.card-success {
    background: linear-gradient(135deg, rgba(52, 168, 83, 0.05) 0%, rgba(70, 192, 99, 0.05) 100%);
    border-left: 4rpx solid #34A853;
  }
  
  &.card-warning {
    background: linear-gradient(135deg, rgba(251, 188, 4, 0.05) 0%, rgba(249, 171, 0, 0.05) 100%);
    border-left: 4rpx solid #FBBC04;
  }
  
  &.card-error {
    background: linear-gradient(135deg, rgba(234, 67, 53, 0.05) 0%, rgba(211, 59, 44, 0.05) 100%);
    border-left: 4rpx solid #EA4335;
  }
  
  .stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: $spacing-md;
    
    .stat-icon {
      width: 64rpx;
      height: 64rpx;
      border-radius: $radius-medium;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .stat-trend {
      display: flex;
      align-items: center;
      font-size: $font-size-sm;
      font-weight: $font-weight-medium;
      
      text {
        margin-left: $spacing-xs;
      }
    }
  }
  
  .stat-content {
    .stat-value {
      display: block;
      font-size: $font-size-xxl;
      font-weight: $font-weight-bold;
      line-height: $line-height-tight;
      margin-bottom: $spacing-xs;
    }
    
    .stat-label {
      display: block;
      font-size: $font-size-base;
      font-weight: $font-weight-medium;
      color: $text-primary;
      margin-bottom: $spacing-xs;
    }
    
    .stat-subtitle {
      display: block;
      font-size: $font-size-sm;
      color: $text-secondary;
    }
  }
  
  .stat-progress {
    margin-top: $spacing-md;
    
    .progress-bar {
      width: 100%;
      height: 8rpx;
      background: $bg-tertiary;
      border-radius: 4rpx;
      overflow: hidden;
      margin-bottom: $spacing-xs;
      
      .progress-fill {
        height: 100%;
        border-radius: 4rpx;
        transition: width 0.3s ease;
      }
    }
    
    .progress-text {
      font-size: $font-size-sm;
      color: $text-secondary;
      text-align: right;
      display: block;
    }
  }
}
</style>
