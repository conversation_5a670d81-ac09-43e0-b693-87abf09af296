<template>
  <view class="modern-navbar" :style="navbarStyle">
    <view class="navbar-content" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left">
        <view v-if="showBack" class="back-button" @click="handleBack">
          <u-icon name="arrow-left" :size="32" :color="iconColor" />
        </view>
        <slot name="left"></slot>
      </view>
      
      <view class="navbar-center">
        <text v-if="title" class="navbar-title" :style="{ color: titleColor }">{{ title }}</text>
        <slot name="center"></slot>
      </view>
      
      <view class="navbar-right">
        <slot name="right"></slot>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ModernNavbar',
  props: {
    title: {
      type: String,
      default: ''
    },
    showBack: {
      type: Boolean,
      default: true
    },
    background: {
      type: [String, Object],
      default: () => ({ backgroundColor: '#FFFFFF' })
    },
    titleColor: {
      type: String,
      default: '#202124'
    },
    iconColor: {
      type: String,
      default: '#202124'
    },
    borderBottom: {
      type: Boolean,
      default: true
    },
    fixed: {
      type: Boolean,
      default: true
    },
    zIndex: {
      type: Number,
      default: 999
    }
  },
  data() {
    return {
      statusBarHeight: 0,
      navbarHeight: 44
    }
  },
  computed: {
    navbarStyle() {
      const style = {
        zIndex: this.zIndex
      }
      
      if (this.fixed) {
        style.position = 'fixed'
        style.top = 0
        style.left = 0
        style.right = 0
      }
      
      if (typeof this.background === 'string') {
        style.backgroundColor = this.background
      } else {
        Object.assign(style, this.background)
      }
      
      if (this.borderBottom) {
        style.borderBottom = '1rpx solid #E8EAED'
      }
      
      return style
    }
  },
  mounted() {
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight
    this.navbarHeight = systemInfo.platform === 'ios' ? 44 : 48
  },
  methods: {
    handleBack() {
      this.$emit('back')
      if (getCurrentPages().length > 1) {
        uni.navigateBack()
      } else {
        uni.switchTab({
          url: '/pages/tabBar/Home'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.modern-navbar {
  background: $bg-primary;
  
  .navbar-content {
    display: flex;
    align-items: center;
    height: 88rpx;
    padding: 0 $spacing-lg;
    
    .navbar-left {
      display: flex;
      align-items: center;
      min-width: 120rpx;
      
      .back-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 64rpx;
        height: 64rpx;
        border-radius: $radius-medium;
        transition: all 0.3s ease;
        
        &:active {
          background: rgba(0, 0, 0, 0.05);
        }
      }
    }
    
    .navbar-center {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      
      .navbar-title {
        font-size: $font-size-lg;
        font-weight: $font-weight-semibold;
        text-align: center;
      }
    }
    
    .navbar-right {
      display: flex;
      align-items: center;
      min-width: 120rpx;
      justify-content: flex-end;
    }
  }
}
</style>
